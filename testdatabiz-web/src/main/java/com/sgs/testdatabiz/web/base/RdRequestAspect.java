package com.sgs.testdatabiz.web.base;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import com.sgs.framework.core.base.BaseProductLine;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.tool.utils.DateUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.HeaderHelper;
import com.sgs.preorder.facade.model.info.LabInfo;
import com.sgs.testdatabiz.core.common.RdRequestContext;
import com.sgs.testdatabiz.core.constants.Constants;
import com.sgs.testdatabiz.dbstorages.mybatis.config.ProductLineContextHolder;
import com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.ApiRequestMapper;
import com.sgs.testdatabiz.domain.service.infrastructure.api.IdService;
import com.sgs.testdatabiz.facade.model.base.BaseModel;
import com.sgs.testdatabiz.integration.FrameWorkClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import com.sgs.testdatabiz.dbstorages.mybatis.model.ApiRequestPO;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.sgs.testdatabiz.core.constants.Constants.*;

/**
 * <AUTHOR>
 * @date 2023/3/28 10:39
 */
@Aspect
@Component
@Order(4)
@Slf4j
public class RdRequestAspect {

    @Resource
    private FrameWorkClient frameWorkClient;

    @Resource
    private ApiRequestMapper apiRequestMapper;

    @Resource
    private IdService idService;

    @Resource
    private ObjectMapper objectMapper;

    @Autowired
    private RedissonClient redissonClient;


    private static String TRACEABILITY_ID = "traceabilityId";

    @Pointcut("execution(* com.sgs.testdatabiz.web.controllers.ReportDataController.*(..)) && args(reqObject)")
    public void aspect(BaseProductLine reqObject) {

    }

    @Pointcut("execution(* com.sgs.testdatabiz.facade.v2.*Facade.*(*)) && args(reqObject)")
    public void testdataServicePointcut(BaseProductLine reqObject){
    }

    /**
     * @param point
     * @return
     */
    @Around("aspect(reqObject) || testdataServicePointcut(reqObject)")
//    @Around("aspect(reqObject)")
    public Object around(ProceedingJoinPoint point, BaseProductLine reqObject) throws Throwable {
        Object resp = null;
        RLock rLock;
        String requestIdParam = HeaderHelper.getParamValue(REQUEST_ID);
        String labCodeParam = HeaderHelper.getParamValue(LAB_CODE);
        String systemIdParam = HeaderHelper.getParamValue(SYSTEM_ID);
        log.info("sci request systemId={},labCode={},requestId={}", systemIdParam, labCodeParam, requestIdParam);

        Assert.isTrue(StringUtils.isNotBlank(requestIdParam), ResponseCode.INTERNAL_SERVER_ERROR, "The requestId header is required ");
//        Assert.isTrue(StringUtils.isNotBlank(labCodeParam), ResponseCode.INTERNAL_SERVER_ERROR, "The labCode header is required ");
        // requestId保存到 redis, 五分钟过期，五分钟内再次请求失败
        String requestKey = String.format(String.format("SCI:TRF:SYSTEM:%s:%s", systemIdParam, requestIdParam).toUpperCase());

        rLock = redissonClient.getLock(requestKey);
        Assert.isTrue(rLock.tryLock(1, 5, TimeUnit.SECONDS), ResponseCode.INTERNAL_SERVER_ERROR, "The requestId repeated");
        LabInfo labInfo = frameWorkClient.getLabCodeInfoByLabCodeFromCache(labCodeParam,systemIdParam);
        if (Func.isEmpty(labInfo) || Func.isEmpty(labInfo.getLaboratoryCode())) {
            throw new BizException("labCode not found");
        }
//        Assert.isTrue(labInfo != null, ResponseCode.INTERNAL_SERVER_ERROR, "labCode not found");
//        Assert.isTrue(Func.isNotEmpty(labInfo.getLaboratoryCode()), ResponseCode.INTERNAL_SERVER_ERROR, "labCode not found");
        if (Objects.nonNull(reqObject)) {
            if (Func.isNotEmpty(ProductLineContextHolder.getProductLineCode())) {
                reqObject.setProductLineCode(ProductLineContextHolder.getProductLineCode());
            }
            if (reqObject instanceof BaseModel) {
                BaseModel sciBaseRequest = (BaseModel) reqObject;
                sciBaseRequest.setSystemId(Func.toLong(systemIdParam));
                sciBaseRequest.setLabId(Func.toLong(labInfo.getLaboratoryID()));
                sciBaseRequest.setLabCode(labCodeParam);
                sciBaseRequest.setRequestId(requestIdParam);
                sciBaseRequest.setBuId(Func.toLong(labInfo.getProductLineID()));
                sciBaseRequest.setBuCode(Func.toStr(labInfo.getProductLineAbbr(),null));
                String traceabilityId = MDC.get(TRACEABILITY_ID);
                if (StringUtils.isNotEmpty(traceabilityId)) {
                    sciBaseRequest.setTraceabilityId(traceabilityId);
                }
            }
        }
        resp = point.proceed(new Object[]{reqObject});

        return resp;
    }

    private Map<String, String> requestHeader() {
        HashMap<String, String> requestHeaderMap = Maps.newHashMap();
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null) {
            return requestHeaderMap;
        }
        ServletRequestAttributes sra = (ServletRequestAttributes) requestAttributes;
        HttpServletRequest request = sra.getRequest();
        if (Func.isEmpty(request)) {
            return requestHeaderMap;
        }
        Enumeration<String> headerNames = request.getHeaderNames();
        if (Func.isEmpty(headerNames)) {
            return requestHeaderMap;
        }
        for (; headerNames.hasMoreElements(); ) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            requestHeaderMap.put(headerName, headerValue);
        }
        return requestHeaderMap;

    }

    /**
     * @param point
     * @param reqObject
     */
    @After("aspect(reqObject) || testdataServicePointcut(reqObject)")
    public void after(JoinPoint point, BaseProductLine reqObject) {
        ProductLineContextHolder.clear();
    }

}
