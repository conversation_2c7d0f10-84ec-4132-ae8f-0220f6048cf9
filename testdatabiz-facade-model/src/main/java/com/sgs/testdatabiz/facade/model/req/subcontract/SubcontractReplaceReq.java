package com.sgs.testdatabiz.facade.model.req.subcontract;

import com.sgs.framework.core.base.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;

/**
 * 分包数据ReportNo替换请求类
 * 
 * 用于将原分包数据设置为无效，并新增一条使用新ReportNo的数据
 * 实现通过原数据无效+新增数据的方式替换ReportNo
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@ToString
@ApiModel(value = "SubcontractReplaceReq", description = "分包数据ReportNo替换请求")
public class SubcontractReplaceReq extends BaseRequest {

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号", required = true, example = "ORD-2024-001")
    @NotEmpty(message = "订单号不能为空")
    private String orderNo;

    /**
     * 实验室代码
     */
    @ApiModelProperty(value = "实验室代码", required = true, example = "GZ")
    @NotEmpty(message = "实验室代码不能为空")
    private String labCode;

    /**
     * 原报告号
     */
    @ApiModelProperty(value = "原报告号", required = true, example = "RPT-2024-001")
    @NotEmpty(message = "原报告号不能为空")
    private String reportNo;

    /**
     * 新报告号
     */
    @ApiModelProperty(value = "新报告号", required = true, example = "RPT-2024-002")
    @NotEmpty(message = "新报告号不能为空")
    private String newReportNo;

}