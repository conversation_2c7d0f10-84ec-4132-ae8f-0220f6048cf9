package com.sgs.testdatabiz.facade.model.testdata;

import io.swagger.annotations.ApiModelProperty;

public class TestDataTestResultRsp {
    /**
     *
     */
    @ApiModelProperty("ReportLimit")
    private String reportLimit;

    /**
     *
     */
    @ApiModelProperty("analyteType；0：General、1：Conclusion")
    private Integer analyteType;

    /**
     *
     */
    @ApiModelProperty("TestAnalyteName")
    private String testAnalyteName;

    /**
     *
     */
    @ApiModelProperty("TestAnalyteName 中文")
    private String testAnalyteNameCN;

    /**
     *
     */
    @ApiModelProperty("reportUnit")
    private String reportUnit;

    /**
     *
     */
    @ApiModelProperty("reportUnitCN 中文")
    private String reportUnitCN;

    /**
     *
     */
    @ApiModelProperty("TestValue")
    private String testValue;

    /**
     *
     */
    @ApiModelProperty("analyte排序字段 analyteSeq")
    private String analyteSeq;

    public String getReportLimit() {
        return reportLimit;
    }

    public void setReportLimit(String reportLimit) {
        this.reportLimit = reportLimit;
    }

    public Integer getAnalyteType() {
        return analyteType;
    }

    public void setAnalyteType(Integer analyteType) {
        this.analyteType = analyteType;
    }

    public String getTestAnalyteName() {
        return testAnalyteName;
    }

    public void setTestAnalyteName(String testAnalyteName) {
        this.testAnalyteName = testAnalyteName;
    }

    public String getTestAnalyteNameCN() {
        return testAnalyteNameCN;
    }

    public void setTestAnalyteNameCN(String testAnalyteNameCN) {
        this.testAnalyteNameCN = testAnalyteNameCN;
    }

    public String getReportUnit() {
        return reportUnit;
    }

    public void setReportUnit(String reportUnit) {
        this.reportUnit = reportUnit;
    }

    public String getReportUnitCN() {
        return reportUnitCN;
    }

    public void setReportUnitCN(String reportUnitCN) {
        this.reportUnitCN = reportUnitCN;
    }

    public String getTestValue() {
        return testValue;
    }

    public void setTestValue(String testValue) {
        this.testValue = testValue;
    }

    public String getAnalyteSeq() {
        return analyteSeq;
    }

    public void setAnalyteSeq(String analyteSeq) {
        this.analyteSeq = analyteSeq;
    }
}
