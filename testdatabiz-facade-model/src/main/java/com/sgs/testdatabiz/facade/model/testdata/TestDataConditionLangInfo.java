package com.sgs.testdatabiz.facade.model.testdata;

import com.sgs.framework.core.common.PrintFriendliness;
import io.swagger.annotations.ApiModelProperty;

public class TestDataConditionLangInfo extends PrintFriendliness {
    /**
     *
     */
    @ApiModelProperty("languageId")
    private Integer languageId;

    /**
     *
     */
    @ApiModelProperty("testConditionName")
    private String testConditionName;

    public Integer getLanguageId() {
        return languageId;
    }

    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    public String getTestConditionName() {
        return testConditionName;
    }

    public void setTestConditionName(String testConditionName) {
        this.testConditionName = testConditionName;
    }
}
