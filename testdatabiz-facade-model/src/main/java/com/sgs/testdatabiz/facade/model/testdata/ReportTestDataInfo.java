package com.sgs.testdatabiz.facade.model.testdata;

import com.sgs.framework.core.base.BaseRequest;
import com.sgs.otsnotes.facade.model.annotation.DeclaredField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;

/**
 * 标准检测数据信息
 * <AUTHOR>
 * @create 2023-03-08 14:55
 */
@ApiModel
@ToString
public class ReportTestDataInfo extends BaseRequest {
    /**
     * 订单号 00
     */
    @ApiModelProperty("订单号 取值规则： \n \n " +
            "   StarLims：取externalParentId  \n "+
            "   SLIM：  \n "+
            "   FAST：  \n "+
            "")
    private String orderNo;

    /**
     * 发包方订单号
     */
    @ApiModelProperty("发包方订单号")
    private String parentOrderNo;

    /**
     * 分包号
     */
    @ApiModelProperty("分包号 取值规则： \n \n " +
            "   StarLims：取externalId  \n "+
            "   SLIM：  \n "+
            "   FAST：  \n "+
            "")
    private String subContractNo;

    /**
     * Lab Code(GZ SL)
     */
    @NotEmpty
    @ApiModelProperty("Lab Code(GZ SL)")
    private String labCode;

    /**
     * 报告号
     */
    @ApiModelProperty("报告号")
    private String reportNo;

    /**
     * 1、当Slim数据时，该值为SubContractNo
     * 2、当Fast数据时，该值为JobNo
     * 3、当Starlims数据时，该值为SubContractNo
     */
    @ApiModelProperty("对象编号 取值规则： \n \n " +
            "   当Slim数据时，该值为SubContractNo  \n "+
            "   当Fast数据时，该值为JobNo  \n "+
            "   当Starlims数据时，该值为SubContractNo  \n "+
            "")
    private String objectNo;

    /**
     * 外部主键Id
     */
    @ApiModelProperty("外部主键Id")
    private String externalId;

    /**
     * 1、当Slim数据时，该值为SlimJobNo
     * 2、当Fast数据时，该值为JobNo
     * 3、当Starlims数据时，该值为folderNo
     */
    @ApiModelProperty("外部编号 取值规则： \n \n " +
            "   当Slim数据时，该值为SlimJobNo  \n "+
            "   当Fast数据时，该值为JobNo  \n "+
            "   当Starlims数据时，该值为folderNo  \n "+
            "")
    private String externalNo;

    /**
     *
     */
    @ApiModelProperty("外部对象编号 取值规则： \n \n " +
            "   StarLims：取originalReportNo  \n "+
            "")
    private String externalObjectNo;

    /**
     *
     */
    @ApiModelProperty("原报告编号 目前只限于StarLims：取originalReportNo")
    private String originalReportNo;

    /**
     *
     */
    @ApiModelProperty("Test Matrix")
    private List<TestDataTestMatrixInfo> testMatrixs;

    /**
     * 完成日期
     */
    @ApiModelProperty("完成日期")
    private Date completedDate;

    /**
     * 1、slim
     * 2、job
     * 3、starlims
     * 4、fast
     * 5、Internal Subcontract
     * 6、Enter subcontract
     */
    @ApiModelProperty("数据来源 取值规则： \n \n " +
            "   1、SLIM  \n "+
            "   2、JOB  \n "+
            "   3、STARLIMS  \n "+
            "   4、FAST  \n "+
            "   5、Internal Subcontract  \n "+
            "   6、Enter Subcontract  \n "+
            "")
    private Integer sourceType;

    /**
     * 多语言（1：英文、2：中文）
     */
    @ApiModelProperty("多语言（1：英文、2：中文）")
    private Integer languageId;

    /**
     * 操作账号
     */
    @ApiModelProperty("操作账号")
    private String regionAccount;

    // add 231114
    private String excludeCustomerInterface;

    public void setSourceType(Integer sourceType) {
        this.sourceType = sourceType;
    }

    public String getExcludeCustomerInterface() {
        return excludeCustomerInterface;
    }

    public void setExcludeCustomerInterface(String excludeCustomerInterface) {
        this.excludeCustomerInterface = excludeCustomerInterface;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getParentOrderNo() {
        return parentOrderNo;
    }

    public void setParentOrderNo(String parentOrderNo) {
        this.parentOrderNo = parentOrderNo;
    }

    public String getSubContractNo() {
        return subContractNo;
    }

    public void setSubContractNo(String subContractNo) {
        this.subContractNo = subContractNo;
    }

    public String getLabCode() {
        return labCode;
    }

    public void setLabCode(String labCode) {
        this.labCode = labCode;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public String getObjectNo() {
        return objectNo;
    }

    public void setObjectNo(String objectNo) {
        this.objectNo = objectNo;
    }

    public String getExternalId() {
        return externalId;
    }

    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }

    public String getExternalNo() {
        return externalNo;
    }

    public void setExternalNo(String externalNo) {
        this.externalNo = externalNo;
    }

    public String getExternalObjectNo() {
        return externalObjectNo;
    }

    public void setExternalObjectNo(String externalObjectNo) {
        this.externalObjectNo = externalObjectNo;
    }

    public String getOriginalReportNo() {
        return originalReportNo;
    }

    public void setOriginalReportNo(String originalReportNo) {
        this.originalReportNo = originalReportNo;
    }

    public List<TestDataTestMatrixInfo> getTestMatrixs() {
        return testMatrixs;
    }

    public void setTestMatrixs(List<TestDataTestMatrixInfo> testMatrixs) {
        this.testMatrixs = testMatrixs;
    }

    public Date getCompletedDate() {
        return completedDate;
    }

    public void setCompletedDate(Date completedDate) {
        this.completedDate = completedDate;
    }

    public int getSourceType() {
        return sourceType;
    }

    public void setSourceType(int sourceType) {
        this.sourceType = sourceType;
    }

    public Integer getLanguageId() {
        return languageId;
    }

    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    public String getRegionAccount() {
        return regionAccount;
    }

    public void setRegionAccount(String regionAccount) {
        this.regionAccount = regionAccount;
    }

}
