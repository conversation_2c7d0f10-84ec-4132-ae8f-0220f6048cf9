package com.sgs.testdatabiz.facade.model.info;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sgs.framework.core.common.PrintFriendliness;
import com.sgs.testdatabiz.facade.model.testdata.TestDataConditionInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;
@Data
public class TestDataInfo extends PrintFriendliness {
    @ApiModelProperty("订单号")
    private String orderNo;
    @ApiModelProperty("多语言（1：英文、2：中文")
    private Integer languageId;
    @ApiModelProperty("分包单号/JobNos")
    private String objectNo;
    @ApiModelProperty("slimJobNo")
    private String externalNo;
    @ApiModelProperty("外部号")
    private String externalCode;
    @ApiModelProperty("完成时间")
    private Date completedDate;
    @ApiModelProperty("sampleNo")
    private String sampleNo;

    @ApiModelProperty("testDataMatrixId")
    private Long testDataMatrixId;

    @ApiModelProperty("testLineMappingId")
    private Integer testLineMappingId;

    @ApiModelProperty("matrixConclusionId")
    private Integer matrixConclusionId;

    @ApiModelProperty("conclusionDisplay")
    private String conclusionDisplay;

    @ApiModelProperty("外部 SampleNo")
    private String externalSampleNo;
    @ApiModelProperty("ReportLimit")
    private String reportLimit;
    @ApiModelProperty("analyteCode")
    private String analyteCode;
    @ApiModelProperty("analyteType；0：General、1：Conclusion")
    private String analyteType;
    @ApiModelProperty("extFields")
    private String extFields;
    @ApiModelProperty("condition")
    private String condition;
    @ApiModelProperty("testConditions")
    private List<TestDataConditionInfo> testConditions;
    @ApiModelProperty("TestAnalyteName")
    private String testAnalyteName;
    @ApiModelProperty("TestAnalyteName 中文")
    private String testAnalyteNameCN;
    @ApiModelProperty("reportUnit")
    private String reportUnit;
    @ApiModelProperty("reportUnitCN 中文")
    private String reportUnitCN;
    @ApiModelProperty("TestValue")
    private String testValue;
    @ApiModelProperty("analyte排序字段 analyteSeq")
    private String analyteSeq;
    @ApiModelProperty("MaterialName")
    private String materialName;
    @ApiModelProperty("MaterialTexture")
    private String materialTexture;
    @ApiModelProperty("UsedPosition")
    private String usedPosition;
    @ApiModelProperty("MaterialColor")
    private String materialColor;
    @ApiModelProperty("testLineId")
    private Integer testLineId;
    @ApiModelProperty("citationId")
    private Integer citationId;
    @ApiModelProperty("testDataLanguages")
    private String testDataLanguages;
    @ApiModelProperty("sourceType; 1:slim   2:fast")
    private Integer sourceType;

    private Integer ppNo;

    private Integer ppVersionId;

    private Long testLineSeq;

    @JsonIgnore
    private Integer testConditionId;
    @JsonIgnore
    private String testConditionDesc;
    @JsonIgnore
    private Integer testConditionSeq;

    // add 231114
    private String excludeCustomerInterface;

    private List<TestDataMatrixExtFieldLangInfo> languageList;

    @ApiModelProperty("testDataPosition")
    private String testDataPosition;
    @ApiModelProperty("testDataId")
    private String testDataId;
    @ApiModelProperty("testDataSpecimenDesc")
    private String testDataSpecimenDesc;
    @ApiModelProperty("testDataUpSpecimenDesc")
    private String testDataUpSpecimenDesc;
    @ApiModelProperty("testDataProcedureConditionName")
    private String  testDataProcedureConditionName;
    @ApiModelProperty("testDataConditionName")
    private String testDataConditionName;
    @ApiModelProperty("testDataParentConditionName")
    private String testDataParentConditionName;
}
