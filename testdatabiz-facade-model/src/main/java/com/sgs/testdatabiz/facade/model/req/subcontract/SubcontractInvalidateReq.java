package com.sgs.testdatabiz.facade.model.req.subcontract;

import com.sgs.framework.core.base.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;

/**
 * 分包数据无效化请求类
 * 
 * 用于将指定分包数据设置为无效状态
 * 通过更新ActiveIndicator字段为0实现软删除
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
@ApiModel(value = "SubcontractInvalidateReq", description = "分包数据无效化请求")
public class SubcontractInvalidateReq extends BaseRequest {

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号", required = true, example = "ORD-2024-001")
    @NotEmpty(message = "订单号不能为空")
    private String orderNo;

    /**
     * 对象编号/分包号
     */
    @ApiModelProperty(value = "对象编号/分包号", required = false, example = "SC-2024-001")
    @NotEmpty(message = "对象编号不能为空")
    private String objectNo;

    /**
     * 实验室代码
     */
    @ApiModelProperty(value = "实验室代码", required = true, example = "GZ")
    @NotEmpty(message = "实验室代码不能为空")
    private String labCode;

    /**
     * 报告号
     */
    @ApiModelProperty(value = "报告号", required = true, example = "RPT-2024-001")
    @NotEmpty(message = "报告号不能为空")
    private String reportNo;
}