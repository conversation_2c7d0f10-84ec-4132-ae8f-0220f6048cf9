package com.sgs.testdatabiz.facade.model.testdata;

import com.sgs.framework.core.common.PrintFriendliness;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TestDataResultLangInfo extends PrintFriendliness {
    /**
     * 多语言（1：英文、2：中文）
     */
    @ApiModelProperty("多语言（1：英文、2：中文）")
    private Integer languageId;

    /**
     *
     */
    @ApiModelProperty("分析项名称 取值规则： \n \n " +
            "   StarLims：取analyteAlias  \n "+
            "   SLIM：取LSA_DESC  \n "+
            "")
    private String testAnalyteName;

    //SCI-1770
    /**
     *
     */
    @ApiModelProperty("条件")
    private String conditionName;
    @ApiModelProperty("父条件")
    private String parentConditionName;
    @ApiModelProperty("过程条件")
    private String procedureConditionName;
    @ApiModelProperty("样品位置")
    private String position;
    @ApiModelProperty("平行样品")
    private String specimenDesc;
    @ApiModelProperty("上级平行样品")
    private String upSpecimenDesc;
    @ApiModelProperty("分析项别名")
    private String analyteAlias;
    /**
     *
     */
    @ApiModelProperty("测试单位")
    private String reportUnit;

    /**
     *
     */
    @ApiModelProperty("测试单位 取值规则： \n \n " +
            "   StarLims：取limit  \n "+
            "")
    private String limitUnit;
}
