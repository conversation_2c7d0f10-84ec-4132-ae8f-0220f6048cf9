package com.sgs.testdatabiz.core.errorcode;

import com.sgs.testdatabiz.core.errorcode.enums.ErrorBizModelEnum;
import com.sgs.testdatabiz.core.errorcode.enums.ErrorCategoryEnum;
import com.sgs.testdatabiz.core.errorcode.enums.ErrorFunctionTypeEnum;
import com.sgs.testdatabiz.core.errorcode.enums.ErrorTypeEnum;

/**
 * 分包数据管理相关错误码定义
 *
 * 定义分包数据查询、替换、无效化等操作的标准错误码
 * 遵循项目错误码规范：SCI.{错误分类}.{业务模块}{功能类型}{错误类型}
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
public class SubcontractErrorCodes {

    /**
     * 分包数据参数校验 - 请求对象为空
     * 错误码：SCI.300.0201
     */
    public static final ErrorCode SUBCONTRACT_REQUEST_NULL = ErrorCodeFactory.createNewErrorCode(
            ErrorCategoryEnum.TEST_DATA_ERROR,
            ErrorBizModelEnum.SUB_CONTACT_TEST_DATA_BIZ,
            ErrorFunctionTypeEnum.VALIDATION,
            ErrorTypeEnum.REQUESTNULL
    );

    /**
     * 分包数据参数校验 - 缺少必需字段
     * 错误码：SCI.300.0202
     */
    public static final ErrorCode SUBCONTRACT_REQUIRED_MISSING = ErrorCodeFactory.createNewErrorCode(
            ErrorCategoryEnum.TEST_DATA_ERROR,
            ErrorBizModelEnum.SUB_CONTACT_TEST_DATA_BIZ,
            ErrorFunctionTypeEnum.VALIDATION,
            ErrorTypeEnum.REQUIREDMISSING
    );

    /**
     * 分包数据业务校验 - 数据校验失败
     * 错误码：SCI.300.0204
     */
    public static final ErrorCode SUBCONTRACT_DATA_VALIDATE_FAIL = ErrorCodeFactory.createNewErrorCode(
            ErrorCategoryEnum.TEST_DATA_ERROR,
            ErrorBizModelEnum.SUB_CONTACT_TEST_DATA_BIZ,
            ErrorFunctionTypeEnum.VALIDATION,
            ErrorTypeEnum.DATA_VALIDATE_FAIL
    );
}