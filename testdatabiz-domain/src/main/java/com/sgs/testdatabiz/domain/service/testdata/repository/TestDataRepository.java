package com.sgs.testdatabiz.domain.service.testdata.repository;

import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataObjectRelPO;

import java.util.List;

/**
 * 测试数据仓储接口
 * 提供测试数据相关的数据访问抽象，封装底层Mapper调用
 * 
 * <AUTHOR>
 */
public interface TestDataRepository {

    // ==================== TestDataObjectRel 相关操作 ====================

    /**
     * 批量插入测试数据对象关系
     * 
     * @param objectRels 对象关系列表
     * @return 插入成功的记录数
     * @throws RuntimeException 当插入失败时抛出异常
     */
    Integer batchInsertObjectRelations(List<TestDataObjectRelPO> objectRels);

    /**
     * 根据对象关系信息查找已存在的对象关系
     * 
     * @param objectRel 查询条件对象
     * @return 找到的对象关系，如果不存在则返回null
     */
    TestDataObjectRelPO findObjectRelation(TestDataObjectRelPO objectRel);

    // ==================== TestDataMatrix 相关操作 ====================

    /**
     * 批量插入测试数据矩阵
     * 
     * @param testMatrices 测试矩阵列表
     * @param suffix       表后缀
     * @return 插入成功的记录数
     * @throws RuntimeException 当插入失败时抛出异常
     */
    Integer batchInsertMatrices(List<TestDataMatrixInfoPO> testMatrices, String suffix);

    /**
     * 查询指定对象关系的现有矩阵数据
     * 
     * @param objectRelId 对象关系ID
     * @param suffix      表后缀
     * @return 现有矩阵数据列表
     */
    List<TestDataMatrixInfoPO> findExistingMatrices(String objectRelId, String suffix);

    // ==================== TestData 相关操作 ====================

    /**
     * 批量插入测试数据
     * 
     * @param testData 测试数据列表
     * @param suffix   表后缀
     * @return 插入成功的记录数
     * @throws RuntimeException 当插入失败时抛出异常
     */
    Integer batchInsertTestData(List<TestDataInfoPO> testData, String suffix);

    /**
     * 查询指定对象关系的现有测试数据
     * 
     * @param objectRelId 对象关系ID
     * @param suffix      表后缀
     * @return 现有测试数据列表
     */
    List<TestDataInfoPO> findExistingTestData(String objectRelId, String suffix);
}