package com.sgs.testdatabiz.domain.service.testdata.exception;

/**
 * 对象关系异常类
 * 
 * 用于处理测试数据保存过程中与对象关系相关的错误，
 * 如对象关系创建失败、查找失败、更新失败等情况。
 * 
 * <AUTHOR>
 */
public class ObjectRelationException extends TestDataSaveException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 报告编号
     */
    private final String reportNo;
    
    /**
     * 对象编号
     */
    private final String objectNo;
    
    /**
     * 外部编号
     */
    private final String externalNo;
    
    /**
     * 构造函数
     * 
     * @param message 错误消息
     */
    public ObjectRelationException(String message) {
        super(message);
        this.reportNo = null;
        this.objectNo = null;
        this.externalNo = null;
    }
    
    /**
     * 构造函数
     * 
     * @param message 错误消息
     * @param cause 原始异常
     */
    public ObjectRelationException(String message, Throwable cause) {
        super(message, cause);
        this.reportNo = null;
        this.objectNo = null;
        this.externalNo = null;
    }
    
    /**
     * 构造函数
     * 
     * @param message 错误消息
     * @param reportNo 报告编号
     * @param objectNo 对象编号
     * @param externalNo 外部编号
     */
    public ObjectRelationException(String message, String reportNo, String objectNo, String externalNo) {
        super(message, "OBJECT_RELATION_ERROR", String.format("reportNo=%s, objectNo=%s, externalNo=%s", reportNo, objectNo, externalNo));
        this.reportNo = reportNo;
        this.objectNo = objectNo;
        this.externalNo = externalNo;
    }
    
    /**
     * 构造函数
     * 
     * @param message 错误消息
     * @param cause 原始异常
     * @param reportNo 报告编号
     * @param objectNo 对象编号
     * @param externalNo 外部编号
     */
    public ObjectRelationException(String message, Throwable cause, String reportNo, String objectNo, String externalNo) {
        super(message, cause, "OBJECT_RELATION_ERROR", String.format("reportNo=%s, objectNo=%s, externalNo=%s", reportNo, objectNo, externalNo));
        this.reportNo = reportNo;
        this.objectNo = objectNo;
        this.externalNo = externalNo;
    }
    
    /**
     * 获取报告编号
     * 
     * @return 报告编号
     */
    public String getReportNo() {
        return reportNo;
    }
    
    /**
     * 获取对象编号
     * 
     * @return 对象编号
     */
    public String getObjectNo() {
        return objectNo;
    }
    
    /**
     * 获取外部编号
     * 
     * @return 外部编号
     */
    public String getExternalNo() {
        return externalNo;
    }
    
    /**
     * 创建对象关系创建失败异常
     * 
     * @param reportNo 报告编号
     * @param objectNo 对象编号
     * @param externalNo 外部编号
     * @return 对象关系异常实例
     */
    public static ObjectRelationException createObjectRelationCreationException(String reportNo, String objectNo, String externalNo) {
        return new ObjectRelationException(
            "无法创建有效的对象关系",
            reportNo,
            objectNo,
            externalNo
        );
    }
    
    /**
     * 创建对象关系查找失败异常
     * 
     * @param reportNo 报告编号
     * @param objectNo 对象编号
     * @param externalNo 外部编号
     * @param cause 原始异常
     * @return 对象关系异常实例
     */
    public static ObjectRelationException createObjectRelationQueryException(String reportNo, String objectNo, String externalNo, Throwable cause) {
        return new ObjectRelationException(
            "对象关系查询失败",
            cause,
            reportNo,
            objectNo,
            externalNo
        );
    }
}