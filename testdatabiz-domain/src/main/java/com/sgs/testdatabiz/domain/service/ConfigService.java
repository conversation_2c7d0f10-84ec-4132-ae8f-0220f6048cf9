package com.sgs.testdatabiz.domain.service;

import com.sgs.framework.core.base.CustomResult;
import com.sgs.testdatabiz.core.util.StringUtil;
import com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.ConfigExtMapper;
import com.sgs.testdatabiz.facade.model.info.starlims.ColumnInfo;
import com.sgs.testdatabiz.facade.model.req.config.ConfigReq;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 *
 */
@Service
public class ConfigService {
    private static final Logger logger = LoggerFactory.getLogger(ConfigService.class);

    @Autowired
    private ConfigExtMapper configExtMapper;

    /**
     *
     * @param reqObject
     * @return
     */
    public CustomResult createTestDataTable(ConfigReq reqObject) {
        CustomResult rspResult = new CustomResult();
        if (CollectionUtils.isEmpty(reqObject.getLabCodes())) {
            return rspResult.fail("请检查参数labcodes");
        }

        List<String> labCodes = reqObject.getLabCodes();
        for (String labCode: labCodes){


            String matrixTable = "tb_test_data_matrix_info";
            String testDataInfoTable = "tb_test_data_info";

            String newMatrixTable = String.format("%s_%s", matrixTable, StringUtil.getTestDataSuffix(labCode));
            String newTestDataInfoTable = String.format("%s_%s", testDataInfoTable, StringUtil.getTestDataSuffix(labCode));


            Integer checkMatrix = configExtMapper.checkTable(newMatrixTable);
            if(checkMatrix == null || checkMatrix.intValue() < 0){
                configExtMapper.createTestDataConfigTable(matrixTable, newMatrixTable);
            }

            Integer checkTestDataInfoTable = configExtMapper.checkTable(newTestDataInfoTable);
            if(checkTestDataInfoTable == null || checkTestDataInfoTable.intValue() < 0){
                configExtMapper.createTestDataConfigTable(testDataInfoTable, newTestDataInfoTable);
            }
        }
        rspResult.setSuccess(true);
        return rspResult;
    }


    public CustomResult alterTableColumn() {
        CustomResult rspResult = new CustomResult();
        String masterTable = "tb_test_data_matrix_info";
        syncAlterTableColumn(rspResult, masterTable);
        masterTable = "tb_test_data_info";
        syncAlterTableColumn(rspResult, masterTable);
        rspResult.setSuccess(true);
        return rspResult;
    }

    @Nullable
    private CustomResult syncAlterTableColumn(CustomResult rspResult, String masterTable) {
        List<String> tables = getTableNameList(masterTable);
        if(CollectionUtils.isEmpty(tables)){
            return rspResult;
        }
        List<ColumnInfo> masterColumns = configExtMapper.getColumnInfoList(masterTable);
        if (masterColumns.isEmpty()){
            return rspResult;
        }
        String alterName = null;
        for (ColumnInfo column: masterColumns) {
            column.setAlterName(alterName);
            alterName = column.getColumnName();
        }
        for(String tableName : tables){
            if(StringUtils.equalsIgnoreCase(tableName, masterTable)){
                continue;
            }
            List<ColumnInfo> columns = configExtMapper.getColumnInfoList(tableName);
            if(CollectionUtils.isEmpty(columns)){
                continue;
            }
            for (ColumnInfo masterColumn : masterColumns) {
                ColumnInfo column = columns.stream().filter(c -> StringUtils.equalsIgnoreCase(c.getColumnName(),
                        masterColumn.getColumnName())).findFirst().orElse(null);
                if (column == null){
                    column = new ColumnInfo();
                    column.setColumnName(masterColumn.getColumnName());
                    column.setAddColumn(true);
                }
                if (column.hashCode() == masterColumn.hashCode()){
                    continue;
                }
                column.setTableName(tableName);
                column.setColumnDef(masterColumn.getColumnDef());
                column.setIsNull(masterColumn.getIsNull());
                column.setColumnType(masterColumn.getColumnType());
                column.setComment(masterColumn.getComment());
                column.setAlterName(masterColumn.getAlterName());
                configExtMapper.alterTableColumn(column);
            }
        }
        return null;
    }

    public List<String> getTableNameList(String masterTable) {
        String table = masterTable + "%";
        List<String> tables = configExtMapper.getTableInfoList(table);
        return tables;
    }

    public CustomResult alterConfigIndex() {
        CustomResult rspResult = new CustomResult();
        String masterTableName = "tb_test_data_matrix_info";
        syncAlterConfigIndex(rspResult, masterTableName);
        masterTableName = "tb_test_data_info";
        syncAlterConfigIndex(rspResult, masterTableName);
        rspResult.setSuccess(true);
        return rspResult;
    }

    @Nullable
    private CustomResult syncAlterConfigIndex(CustomResult rspResult, String masterTableName) {
        List<String> bizLogTables = getTableNameList(masterTableName);
        if (bizLogTables.isEmpty()){
            return rspResult;
        }
        // 获取所有索引
        List<String> allIndexFromTableName = configExtMapper.getAllIndexNameFromTableName(masterTableName);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(allIndexFromTableName)) {
            return rspResult;
        }
        for (String indexName : allIndexFromTableName) {
            List<String> indexFromIndexName = configExtMapper.getAllIndexFromTableName(masterTableName, indexName);
            for (String tableName : bizLogTables) {
                if (!tableName.startsWith(masterTableName)) {
                    continue;
                }
                List<String> addIndex = configExtMapper.findIndexFromTableName(tableName, indexName);
                if (org.apache.commons.collections4.CollectionUtils.isEmpty(addIndex)) {
                    configExtMapper.commonCreatIndex(tableName, indexName, indexFromIndexName);
                }
            }
        }
        return null;
    }


}
