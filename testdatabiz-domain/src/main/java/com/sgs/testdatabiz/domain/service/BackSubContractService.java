package com.sgs.testdatabiz.domain.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.model.enums.SourceTypeEnum;
import com.sgs.otsnotes.facade.model.enums.StatusEnum;
import com.sgs.testdatabiz.core.annotation.SourceType;
import com.sgs.testdatabiz.core.util.*;
import com.sgs.testdatabiz.dbstorages.mybatis.config.ProductLineContextHolder;
import com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.TestDataMatrixInfoExtMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.TestDataObjectRelExtMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.extmodel.TestDataDTO;
import com.sgs.testdatabiz.dbstorages.mybatis.extmodel.TestDataObjectRelDTO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataObjectRelPO;
import com.sgs.testdatabiz.facade.model.req.backsubcontract.SubCompleteTestDataReq;
import com.sgs.testdatabiz.facade.model.req.backsubcontract.TestDataMatrixReq;
import com.sgs.testdatabiz.facade.model.req.backsubcontract.TestDataResultReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@SourceType(sourceType = SourceTypeEnum.SUBCONTRACT)
public class BackSubContractService extends AbstractTestDataService<SubCompleteTestDataReq> {

    private static final Logger logger = LoggerFactory.getLogger(BackSubContractService.class);

    @Autowired
    private TestDataObjectRelExtMapper objectRelExtMapper;
    @Autowired
    private TestDataMatrixInfoExtMapper matrixInfoExtMapper;
    @Autowired
    private SnowflakeIdWorker idWorker;

    @Override
    protected CustomResult<TestDataDTO> doInvoke(SubCompleteTestDataReq reqObject) {
        CustomResult<TestDataDTO> rspResult = new CustomResult();

        String userName = SourceTypeEnum.SUBCONTRACT.getDesc();
        UserInfo localUser = UserHelper.getLocalUser();
        if (localUser != null) {
            userName = localUser.getRegionAccount();
        }
        TestDataObjectRelPO relPO = new TestDataObjectRelPO();
        List<TestDataMatrixInfoPO> testDataMatrixs = Lists.newArrayList();
        List<TestDataInfoPO> testDataInfos = Lists.newArrayList();
        String suffix = StringUtil.getTestDataSuffix(reqObject.getLabCode());

        relPO.setId(UUID.randomUUID().toString());
        TestDataObjectRelPO reportObjectRel = objectRelExtMapper.getReportObjectRelByObjectNo(reqObject.getObjectNo());
        if (reportObjectRel != null) {
            relPO.setId(reportObjectRel.getId());
        }
        relPO.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        relPO.setLabCode(reqObject.getLabCode());
        relPO.setObjectNo(reqObject.getObjectNo());
        relPO.setOrderNo(reqObject.getOrderNo());
        relPO.setParentOrderNo(StringUtil.defaultString(reqObject.getParentOrderNo(), reqObject.getOrderNo()));
        relPO.setReportNo(reqObject.getReportNo());
        relPO.setCompleteDate(reqObject.getCompletedDate());
        relPO.setExternalId(null);
        relPO.setExternalNo(null);
        relPO.setSourceType(SourceTypeEnum.SUBCONTRACT.getCode());
        relPO.setLanguageId(LanguageType.English.getLanguageId());

        relPO.setBizVersionId(this.getTestDataReportObjectRelMd5(relPO));
        relPO.setActiveIndicator(StatusEnum.VALID.getCode());
        relPO.setCreatedBy(userName);
        relPO.setCreatedDate(DateUtils.getNow());
        relPO.setModifiedBy(userName);
        relPO.setModifiedDate(DateUtils.getNow());

        if (!CollectionUtils.isEmpty(reqObject.getMatrixDTOList())) {
            List<TestDataMatrixReq> testMatrixDTOs = reqObject.getMatrixDTOList();
            Map<String, List<TestDataMatrixReq>> matrixMaps = testMatrixDTOs.stream()
                    .filter(item -> StringUtil.isNotEmpty(item.getTestMatrixId()))
                    .collect(Collectors.groupingBy(TestDataMatrixReq::getTestMatrixId));

            // 获取 matrix 中的 <bizVersionId, id>关系
            List<TestDataMatrixInfoPO> testDataMatrixInfoPOS = matrixInfoExtMapper.queryMatrix(relPO.getId(), suffix);
            Map<String, Long> matrixBizMaps = Maps.newConcurrentMap();
            if (!CollectionUtils.isEmpty(testDataMatrixInfoPOS)) {
                matrixBizMaps = testDataMatrixInfoPOS.stream().collect(Collectors.toMap(TestDataMatrixInfoPO::getBizVersionId, TestDataMatrixInfoPO::getId, (k1, k2) -> k1));
            }

            for (TestDataMatrixReq matrixReq : reqObject.getMatrixDTOList()) {
                TestDataMatrixInfoPO testDataMatrixInfo = new TestDataMatrixInfoPO();
                BeanUtils.copyProperties(matrixReq, testDataMatrixInfo);
                testDataMatrixInfo.setObjectRelId(relPO.getId());

                testDataMatrixInfo.setBizVersionId(this.getTestDataReportMatrixMd5(testDataMatrixInfo));
                // 设置 Id
                testDataMatrixInfo.setId(NumberUtil.defaultIfBlank(matrixBizMaps.get(testDataMatrixInfo.getBizVersionId()), idWorker.nextId()));

                testDataMatrixInfo.setActiveIndicator(StatusEnum.VALID.getCode());
                testDataMatrixInfo.setCreatedBy(userName);
                testDataMatrixInfo.setCreatedDate(DateUtils.getNow());
                testDataMatrixInfo.setModifiedBy(userName);
                testDataMatrixInfo.setModifiedDate(DateUtils.getNow());
                testDataMatrixs.add(testDataMatrixInfo);

                if (CollectionUtils.isEmpty(matrixReq.getResultDTOS())) {
                    continue;
                }
                for (TestDataResultReq resultReq : matrixReq.getResultDTOS()) {
                    TestDataInfoPO testDataInfoPO = new TestDataInfoPO();
                    BeanUtils.copyProperties(resultReq, testDataInfoPO);

                    testDataInfoPO.setId(idWorker.nextId());
                    testDataInfoPO.setObjectRelId(relPO.getId());
                    testDataInfoPO.setBizVersionId(this.getTestDataReportMd5(testDataInfoPO));
                    testDataInfoPO.setActiveIndicator(StatusEnum.VALID.getCode());
                    testDataInfoPO.setCreatedBy(userName);
                    testDataInfoPO.setCreatedDate(DateUtils.getNow());
                    testDataInfoPO.setModifiedBy(userName);
                    testDataInfoPO.setModifiedDate(DateUtils.getNow());
                    testDataInfos.add(testDataInfoPO);
                }
            }
        }

        TestDataDTO testDataDTO = new TestDataDTO();
        testDataDTO.setTestDataObjectRels(Lists.newArrayList(relPO));
        testDataDTO.setTestDataMatrixInfos(testDataMatrixs);
        testDataDTO.setTestDataInfos(testDataInfos);
        testDataDTO.setTestDataSuffix(suffix);

        TestDataObjectRelDTO objectRelDTO = new TestDataObjectRelDTO();
        objectRelDTO.setObjectNo(reqObject.getObjectNo());
        objectRelDTO.setReportNo(reqObject.getReportNo());
        objectRelDTO.setModifiedDate(DateUtils.getNow());
        objectRelDTO.setModifiedBy(userName);
        testDataDTO.setObjectRelDTO(objectRelDTO);

        CustomResult customResult = this.doDeal(testDataDTO);
        if (!customResult.isSuccess()) {
            return rspResult.fail(customResult.getMsg());
        }
        rspResult.setSuccess(true);
        return rspResult;
    }
}
