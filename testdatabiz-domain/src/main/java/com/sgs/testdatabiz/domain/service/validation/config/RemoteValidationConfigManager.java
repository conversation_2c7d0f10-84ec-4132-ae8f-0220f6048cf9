package com.sgs.testdatabiz.domain.service.validation.config;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.sgs.testdatabiz.integration.model.validation.ValidatedFieldResult;

import lombok.extern.slf4j.Slf4j;

/**
 * 远程校验配置管理类
 * 用于管理校验规则配置，支持基于Nacos的动态配置
 * 主要功能：
 * 1. 初始化加载校验规则配置
 * 2. 判断是否需要处理特定的错误
 */
@Component
@Slf4j
public class RemoteValidationConfigManager {

    private final RemoteValidationRuleConfig validationRuleConfig;

    @Autowired
    public RemoteValidationConfigManager(RemoteValidationRuleConfig validationRuleConfig) {
        this.validationRuleConfig = validationRuleConfig;
    }

    @PostConstruct
    public void init() {
        // 记录Nacos配置信息
        log.info("[RemoteValidationConfigManager] 校验规则配置: config={}", validationRuleConfig);
    }

    /**
     * 检查是否需要处理指定的错误
     * 处理逻辑：
     * 1. 如果nacos配置为空或未启用，不处理任何错误
     * 2. 如果错误对象为空，不处理
     * 3. 如果匹配到规则且规则启用，则检查错误字段是否在配置的字段列表中
     * 4. 只有当字段匹配时才处理该错误
     *
     * @param error 错误结果对象，包含校验规则类型和字段信息
     * @return true-需要处理该错误 false-无需处理该错误
     */
    public boolean shouldHandleError(ValidatedFieldResult error) {
        // 1. 快速失败校验：配置为空或未启用时直接返回false
        if (!isValidInput(error)) {
            log.debug("[RemoteValidationConfigManager] 配置无效或错误对象为空，跳过处理");
            return false;
        }

        // 2. 获取对应规则类型的校验规则
        RemoteValidationRule rule = validationRuleConfig.getRules().get(error.getRule());
        
        // 3. 检查规则是否存在且启用
        if (rule == null || !rule.isEnabled() || rule.getFields() == null) {
            log.debug("[RemoteValidationConfigManager] 规则不存在或未启用, rule={}", error.getRule());
            return false;
        }

        // 4. 检查错误字段是否在配置的字段列表中
        return isFieldMatchRule(error, rule);
    }

    /**
     * 检查字段是否匹配规则配置
     * 校验字段路径匹配逻辑:
     * 1. 校验字段路径格式为: trfList.orderList.rootOrderNo 
     * 2. 配置规则中字段格式为: rootOrderNo
     * 3. 需要将校验字段路径的最后一个节点与规则配置的字段进行匹配
     * 4. 如果最后一个节点匹配,则认为该字段需要校验
     *
     * @param error 错误信息
     * @param rule 校验规则
     * @return true-字段匹配 false-字段不匹配
     */
    private boolean isFieldMatchRule(ValidatedFieldResult error, RemoteValidationRule rule) {
        if (error.getField() == null) {
            log.debug("[RemoteValidationConfigManager] 错误字段为空, rule={}, field={}", 
                     error.getRule(), error.getField());
            return false;
        }

        // 获取字段路径的最后一个节点
        String fieldPath = error.getField();
        String lastFieldNode = fieldPath.contains(".") ? 
            fieldPath.substring(fieldPath.lastIndexOf(".") + 1) : fieldPath;
        
        // 检查最后一个节点是否在配置的字段列表中
        boolean isMatch = rule.getFields().contains(lastFieldNode);
        
        if (isMatch) {
            log.debug("[RemoteValidationConfigManager] 匹配到需要处理的错误, rule={}, field={}, lastNode={}", 
                     error.getRule(), error.getField(), lastFieldNode);
        } else {
            log.debug("[RemoteValidationConfigManager] 字段不匹配, rule={}, field={}, lastNode={}", 
                     error.getRule(), error.getField(), lastFieldNode);
        }

        return isMatch;
    }

    /**
     * 校验输入参数是否有效
     * 检查项：
     * 1. 错误对象不为空且包含必要信息
     * 2. 配置对象不为空
     * 3. 配置已启用
     * 4. 规则配置不为空
     *
     * @param error 待校验的错误结果
     * @return true-输入有效 false-输入无效
     */
    private boolean isValidInput(ValidatedFieldResult error) {
        // 首先检查validationRuleConfig是否为空
        if (validationRuleConfig == null) {
            log.debug("[RemoteValidationConfigManager] 校验规则配置为空");
            return false;
        }

        // 检查错误对象及其必要字段
        if (error == null || error.getRule() == null || error.getField() == null) {
            log.debug("[RemoteValidationConfigManager] 错误对象为空或缺少必要信息");
            return false;
        }

        // 检查配置是否启用且包含规则
        if (!validationRuleConfig.isEnabled() || validationRuleConfig.getRules() == null) {
            log.debug("[RemoteValidationConfigManager] 校验规则未启用或规则列表为空");
            return false;
        }

        return true;
    }
} 