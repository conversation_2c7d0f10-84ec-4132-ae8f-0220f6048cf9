package com.sgs.testdatabiz.domain.service;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sgs.extsystem.facade.model.testdata.info.TestDataConfigRuleInfo;
import com.sgs.extsystem.facade.model.util.ExtStringUtil;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.model.enums.OperationType;
import com.sgs.framework.model.enums.ReportLanguage;
import com.sgs.framework.model.enums.SourceTypeEnum;
import com.sgs.framework.tool.utils.Func;
import com.sgs.otsnotes.facade.model.common.BaseResponse;
import com.sgs.otsnotes.facade.model.dto.subcontract.SubContractTestLineDTO;
import com.sgs.otsnotes.facade.model.enums.PriorityLevel;
import com.sgs.otsnotes.facade.model.enums.StatusEnum;
import com.sgs.preorder.facade.CustomerFacade;
import com.sgs.preorder.facade.model.common.ResponseCode;
import com.sgs.preorder.facade.model.req.customer.CustomerSimplifyInfoReq;
import com.sgs.preorder.facade.model.rsp.customer.CustomerSimplifyInfoRsp;
import com.sgs.preorder.facade.model.rsp.order.OrderSimplifyInfoRsp;
import com.sgs.testdatabiz.core.constants.Constants;
import com.sgs.testdatabiz.core.util.LOStringUtil;
import com.sgs.testdatabiz.core.util.NumberUtil;
import com.sgs.testdatabiz.core.util.SnowflakeIdWorker;
import com.sgs.testdatabiz.core.util.StringUtil;
import com.sgs.testdatabiz.facade.model.Helper.AnalyteNameHelper;
import com.sgs.testdatabiz.facade.model.dto.TestLineMappingDTO;
import com.sgs.testdatabiz.facade.model.enums.AnalyteTypeEnum;
import com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.*;
import com.sgs.testdatabiz.dbstorages.mybatis.extmodel.OrderIdRelDTO;
import com.sgs.testdatabiz.dbstorages.mybatis.extmodel.TestDataMappingDTO;
import com.sgs.testdatabiz.dbstorages.mybatis.extmodel.TestDataOldDTO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataObjectRelPO;
import com.sgs.testdatabiz.facade.model.dto.TestDataQueryDTO;
import com.sgs.testdatabiz.facade.model.enums.ErrorEnum;
import com.sgs.testdatabiz.facade.model.info.TestDataMatrixExtFieldInfo;
import com.sgs.testdatabiz.facade.model.dto.slim.SlimTestDataLangInfo;
import com.sgs.testdatabiz.facade.model.info.TestDataInfo;
import com.sgs.testdatabiz.facade.model.info.TestDataMatrixExtFieldLangInfo;
import com.sgs.testdatabiz.facade.model.req.*;
import com.sgs.testdatabiz.facade.model.req.config.ConfigReq;
import com.sgs.testdatabiz.facade.model.rsp.ErrorRsp;
import com.sgs.testdatabiz.facade.model.rsp.TestDataConfigRsp;
import com.sgs.testdatabiz.facade.model.testdata.*;
import com.sgs.testdatabiz.facade.model.rsp.TestLineAnalyteMappingRsp;
import com.sgs.testdatabiz.integration.PreOrderClient;
import com.sgs.testdatabiz.integration.SubcontractClient;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class TestDataService {
    private final Logger logger = LoggerFactory.getLogger(TestDataService.class);

    @Autowired
    private TestDataObjectRelExtMapper testDataReportObjectRelExtMapper;
    @Autowired
    private ConfigExtMapper configExtMapper;
    @Autowired
    private PreOrderClient preOrderClient;
    @Autowired
    private SubcontractClient subcontractClient;
    @Autowired
    private TestDataObjectRelExtMapper objectRelExtMapper;
    @Autowired
    private SlimService slimService;
    @Autowired
    private TestDataInfoExtMapper testDataReporExtMapper;
    @Autowired
    private TestLineAnalyteExtMapper testLineAnalyteExtMapper;
    @Autowired
    private TestDataMatrixInfoExtMapper matrixInfoExtMapper;
    @Autowired
    private TestDataMatrixInfoExtMapper testDataReportMatrixExtMapper;
    @Autowired
    private SnowflakeIdWorker idWorker;
    @Autowired
    private TestLineMappingExtMapper testLineMappingExtMapper;
    @Autowired
    private CustomerFacade customerFacade;
    @Autowired
    private TestDataConfigService queryTestDataConfig;

    /**
     * 查询测试数据（优化版本）
     * 
     * @param reqObject 查询请求参数
     * @return 包含测试数据的自定义结果对象
     * 
     * 流程说明：
     * 1. 参数验证
     * 2. 构建查询条件
     * 3. 获取基础数据
     * 4. 处理分析物映射
     * 5. 处理多语言和扩展字段
     * 6. 返回结果
     */
    public CustomResult<List<TestDataInfo>> queryTestData(TestDataQueryReq reqObject) {
        // 参数验证
        if (validateQueryParams(reqObject).isPresent()) {
            return validateQueryParams(reqObject).get();
        }

        // 构建查询条件
        TestDataQueryDTO queryDTO = buildQueryDTO(reqObject);
        
        // 获取基础数据
        List<TestDataInfo> testDatas = testDataReportObjectRelExtMapper.queryTestData(queryDTO);
        
        // 处理分析物映射
        Map<Integer, Set<String>> testLineAnalyteMappingMaps = getTestLineAnalyteMappingList(reqObject, testDatas);
        // 处理数据和返回结果
        return processTestData(testDatas, testLineAnalyteMappingMaps, reqObject);
    }

    /**
     * 验证查询参数
     * @param reqObject 查询请求参数
     * @return 如果验证失败返回错误结果，否则返回空
     */
    private Optional<CustomResult<List<TestDataInfo>>> validateQueryParams(TestDataQueryReq reqObject) {
        CustomResult<List<TestDataInfo>> rspResult = new CustomResult();
        if (reqObject == null) {
            return Optional.of(rspResult.fail("Parameter can't be null"));
        }
        String productLineCode = reqObject.getProductLineCode();
        String labCode = reqObject.getLabCode();
        if (StringUtils.isBlank(productLineCode) || StringUtils.isBlank(labCode)) {
            return Optional.of(rspResult.fail("Parameter productLineCode or labCode can't be null"));
        }
        if (StringUtils.isBlank(reqObject.getOrderNo()) && CollectionUtils.isEmpty(reqObject.getObjectNos())) {
            return Optional.of(rspResult.fail("Parameter orderNo or objectNos can't be null"));
        }
        if (CollectionUtils.isEmpty(reqObject.getSourceTypes())) {
            return Optional.of(rspResult.fail("Parameter sourceType(1:slim 2:job 3:starlims 4:fast 5:subcontract 6:EnterSubcontract) can't be null"));
        }
        return Optional.empty();
    }

    /**
     * 构建查询DTO
     * @param reqObject 查询请求参数
     * @return 构建好的查询DTO
     */
    private TestDataQueryDTO buildQueryDTO(TestDataQueryReq reqObject) {
        TestDataQueryDTO queryDTO = new TestDataQueryDTO();
        BeanUtils.copyProperties(reqObject, queryDTO);
        
        // 不需要用sample查询
        if (reqObject.getNeedSampleQuery() == null || !reqObject.getNeedSampleQuery()) {
            queryDTO.setSampleNos(Lists.newArrayList());
        }
        // 获取表明后缀
        queryDTO.setLabSuffix(StringUtil.getTestDataSuffix(reqObject.getLabCode()));
        return queryDTO;
    }

    /**
     * 提取SLIM编码
     * @param testDatas 测试数据列表
     * @return 提取的SLIM编码集合
     */
    private Set<String> extractSlimCodes(List<TestDataInfo> testDatas) {
        return testDatas.stream()
            .filter(td -> StringUtils.isNotBlank(td.getExternalCode()))
            .map(TestDataInfo::getExternalCode)
            .collect(Collectors.toSet());
    }

    /**
     * 提取测试线ID
     * @param testDatas 测试数据列表
     * @return 提取的测试线ID集合
     */
    private Set<Integer> extractTestLineIds(List<TestDataInfo> testDatas) {
        return testDatas.stream()
            .map(td -> NumberUtil.toInt(td.getTestLineId()))
            .filter(id -> id > 0)
            .collect(Collectors.toSet());
    }

    /**
     * 构建测试线映射关系
     * @param testLineMapping 测试线映射请求参数
     * @return 构建好的测试线映射关系
     */
    private Map<String, Integer> buildTestLineMappingMaps(TestLineMappingInfoReq testLineMapping) {
        Map<String, Integer> testLineMappingMaps = Maps.newHashMap();
        testLineMappingExtMapper.getTestLineMappingInfoList(testLineMapping)
            .forEach(tlm -> {
                String slimKey = getTestLineMappingKey(tlm.getSlimCode(), 
                    NumberUtil.toInt(tlm.getPpNo()), 
                    tlm.getTestLineId(), 
                    tlm.getStandardId());
                if (StringUtils.isNotBlank(slimKey)) {
                    testLineMappingMaps.put(slimKey, tlm.getId());
                }
            });
        return testLineMappingMaps;
    }

    /**
     * 处理测试数据
     * @param testDatas 测试数据列表
     * @param testLineAnalyteMappingMaps 分析物映射关系
     * @param reqObject 查询请求参数
     * @return 处理后的结果
     */
    private CustomResult<List<TestDataInfo>> processTestData(List<TestDataInfo> testDatas, 
                                                              Map<Integer, Set<String>> testLineAnalyteMappingMaps, 
                                                              TestDataQueryReq reqObject) {
        CustomResult<List<TestDataInfo>> rspResult = new CustomResult();
        
        // 处理数据和返回结果
        testDatas.removeIf(testData -> {
            int testLineMappingId = NumberUtil.toInt(testData.getTestLineMappingId());
            
            // 分析物过滤
            if (StringUtils.isNotBlank(testData.getExternalCode()) && testLineMappingId > 0) {
                Set<String> analyteCodes = testLineAnalyteMappingMaps.get(testLineMappingId);
                if (analyteCodes != null && !analyteCodes.isEmpty() && 
                    !analyteCodes.contains(testData.getAnalyteCode().toUpperCase())) {
                    return true;
                }
            }
            
            // 设置扩展字段
            setMatrixExtField(testData);
            // 设置测试条件
            testData.setTestConditions(getTestConditionList(testData));
            // 处理多语言信息
            processLanguageInfo(testData);
            // 处理扩展字段语言信息
            processExtFieldLanguages(testData);
            
            return false;
        });
        
        rspResult.setData(testDatas);
        rspResult.setSuccess(true);
        return rspResult;
    }

    /**
     * 处理多语言信息
     * @param testData 测试数据
     */
    private void processLanguageInfo(TestDataInfo testData) {
        List<SlimTestDataLangInfo> langs = getTestDataLangInfoList(testData.getTestDataLanguages());
        if (!CollectionUtils.isEmpty(langs)) {
            for (SlimTestDataLangInfo langInfo : langs) {
                if (LanguageType.check(langInfo.getLanguageId(), LanguageType.Chinese)) {
                    testData.setReportUnitCN(langInfo.getReportUnit());
                    testData.setTestAnalyteNameCN(StringUtils.defaultString(langInfo.getTestAnalyteName(), langInfo.getAnalyteName()));
                }
            }
        }
    }

    /**
     * 处理扩展字段语言信息
     * @param testData 测试数据
     */
    private void processExtFieldLanguages(TestDataInfo testData) {
        String extFields = testData.getExtFields();
        if (Func.isNotEmpty(extFields)) {
            TestDataMatrixExtFieldInfo extField = JSONObject.parseObject(extFields, TestDataMatrixExtFieldInfo.class);
            if (Func.isNotEmpty(extField)) {
                Map<Integer, TestDataMatrixExtFieldLangInfo> langMap = Maps.newHashMap();
                // 处理主语言信息
                processMainLanguage(extField, langMap, testData);
                // 处理其他语言
                processOtherLanguages(extField, langMap);
                // 设置结果
                if (Func.isNotEmpty(langMap)) {
                    testData.setLanguageList(new ArrayList<>(langMap.values()));
                }
            }
        }
    }

    /**
     * 处理主语言信息
     * @param extField 扩展字段信息
     * @param langMap 语言映射
     * @param testData 测试数据
     */
    private void processMainLanguage(TestDataMatrixExtFieldInfo extField, 
                                    Map<Integer, TestDataMatrixExtFieldLangInfo> langMap, 
                                    TestDataInfo testData) {
        TestDataMatrixExtFieldLangInfo testDataTestMatrixLangRsp = new TestDataMatrixExtFieldLangInfo();
        if (Func.isNotEmpty(extField.getMaterialColor()) || Func.isNotEmpty(extField.getMaterialTexture()) || 
            Func.isNotEmpty(extField.getMaterialName()) || Func.isNotEmpty(extField.getUsedPosition())) {
            testDataTestMatrixLangRsp.setLanguageId(LanguageType.English.getLanguageId());
            testDataTestMatrixLangRsp.setMaterialColor(extField.getMaterialColor());
            testDataTestMatrixLangRsp.setMaterialName(extField.getMaterialName());
            testDataTestMatrixLangRsp.setMaterialTexture(extField.getMaterialTexture());
            testDataTestMatrixLangRsp.setUsedPosition(extField.getUsedPosition());
            langMap.put(testDataTestMatrixLangRsp.getLanguageId(), testDataTestMatrixLangRsp);
        }
    }

    /**
     * 处理其他语言信息
     * @param extField 扩展字段信息
     * @param langMap 语言映射
     */
    private void processOtherLanguages(TestDataMatrixExtFieldInfo extField, 
                                     Map<Integer, TestDataMatrixExtFieldLangInfo> langMap) {
        List<TestDataMatrixExtFieldLangInfo> languages = extField.getLanguages();
        if (Func.isNotEmpty(languages)) {
            languages.forEach(lang -> {
                Assert.notNull(lang.getLanguageId(), "languageId cannot null!");
                if (Func.isNotEmpty(lang.getMaterialColor()) || Func.isNotEmpty(lang.getMaterialTexture()) || 
                    Func.isNotEmpty(lang.getMaterialName()) || Func.isNotEmpty(lang.getUsedPosition())) {
                    TestDataMatrixExtFieldLangInfo testDataMatrixExtFieldLangInfo = langMap.get(lang.getLanguageId());
                    if (Func.isNotEmpty(testDataMatrixExtFieldLangInfo)) {
                        updateExistingLanguageInfo(testDataMatrixExtFieldLangInfo, lang);
                    } else {
                        TestDataMatrixExtFieldLangInfo testMatrixLangRsp = new TestDataMatrixExtFieldLangInfo();
                        testMatrixLangRsp.setLanguageId(lang.getLanguageId());
                        testMatrixLangRsp.setAppFactorName(lang.getAppFactorName());
                        testMatrixLangRsp.setMaterialName(lang.getMaterialName());
                        testMatrixLangRsp.setMaterialColor(lang.getMaterialColor());
                        testMatrixLangRsp.setUsedPosition(lang.getUsedPosition());
                        testMatrixLangRsp.setMaterialTexture(lang.getMaterialTexture());
                        langMap.put(lang.getLanguageId(), testMatrixLangRsp);
                    }
                }
            });
        }
    }

    /**
     * 更新已存在的语言信息
     * @param existingInfo 已存在的语言信息
     * @param newInfo 新的语言信息
     */
    private void updateExistingLanguageInfo(TestDataMatrixExtFieldLangInfo existingInfo, 
                                            TestDataMatrixExtFieldLangInfo newInfo) {
        if (StringUtils.isBlank(existingInfo.getMaterialName()) && StringUtils.isNotBlank(newInfo.getMaterialName())) {
            existingInfo.setMaterialName(newInfo.getMaterialName());
        }
        if (StringUtils.isBlank(existingInfo.getMaterialColor()) && StringUtils.isNotBlank(newInfo.getMaterialColor())) {
            existingInfo.setMaterialColor(newInfo.getMaterialColor());
        }
        if (StringUtils.isBlank(existingInfo.getUsedPosition()) && StringUtils.isNotBlank(newInfo.getUsedPosition())) {
            existingInfo.setUsedPosition(newInfo.getUsedPosition());
        }
        if (StringUtils.isBlank(existingInfo.getMaterialTexture()) && StringUtils.isNotBlank(newInfo.getMaterialTexture())) {
            existingInfo.setMaterialTexture(newInfo.getMaterialTexture());
        }
    }

    /**
     * @param langs
     * @return
     */
    private List<SlimTestDataLangInfo> getTestDataLangInfoList(String langs) {
        if (StringUtils.isBlank(langs)) {
            return null;
        }
        try {
            return JSONObject.parseArray(langs, SlimTestDataLangInfo.class);
        } catch (Exception ex) {
            return null;
        }
    }

    /**
     * @param testData
     */
    private void setMatrixExtField(TestDataInfo testData) {
        if (StringUtils.isBlank(testData.getExtFields())) {
            return;
        }
        TestDataMatrixExtFieldInfo extField = JSONObject.parseObject(testData.getExtFields(), TestDataMatrixExtFieldInfo.class);
        if (extField == null) {
            return;
        }
        LanguageType languageType = LanguageType.findLanguageId(testData.getLanguageId());
        if (languageType == null) {
            return;
        }
        switch (languageType) {
            case Chinese:
                List<TestDataMatrixExtFieldLangInfo> languages = extField.getLanguages();
                if (languages == null || languages.isEmpty()) {
                    return;
                }
                TestDataMatrixExtFieldLangInfo extFieldLang = languages.stream().filter(lang -> LanguageType.check(lang.getLanguageId(), LanguageType.Chinese)).findFirst().orElse(null);
                if (extFieldLang == null) {
                    return;
                }
                testData.setMaterialColor(extFieldLang.getMaterialColor());
                testData.setMaterialName(extFieldLang.getMaterialName());
                testData.setMaterialTexture(extFieldLang.getMaterialTexture());
                testData.setUsedPosition(extFieldLang.getUsedPosition());
                break;
            case English:
                testData.setMaterialColor(extField.getMaterialColor());
                testData.setMaterialName(extField.getMaterialName());
                testData.setMaterialTexture(extField.getMaterialTexture());
                testData.setUsedPosition(extField.getUsedPosition());
                break;
        }
    }

    /**
     * @param testData
     * @return
     */
    private List<TestDataConditionInfo> getTestConditionList(TestDataInfo testData) {
        if (StringUtils.isBlank(testData.getCondition())) {
            return null;
        }
        try {
            return JSONObject.parseArray(testData.getCondition(), TestDataConditionInfo.class);
        } catch (Exception ex) {
            logger.error("getTestConditionList_{}，Error：{}", testData.getOrderNo(), testData);
            return null;
        }
    }

    /**
     * @param reqObject
     * @param testDatas
     * @return
     */
    private Map<Integer, Set<String>> getTestLineAnalyteMappingList(TestDataQueryReq reqObject, List<TestDataInfo> testDatas) {
        Map<Integer, Set<String>> testLineAnalyteMappingMaps = Maps.newHashMap();
        if (testDatas == null || testDatas.isEmpty()) {
            return testLineAnalyteMappingMaps;
        }
        // TODO 如果查Conclusion就不需要过滤Analyte Mapping
        if (AnalyteTypeEnum.check(reqObject.getAnalyteType(), AnalyteTypeEnum.Conclusion)) {
            return testLineAnalyteMappingMaps;
        }
        // PpVersionId，PpNo
        Map<Integer, Integer> ppMaps = Maps.newHashMap();
        List<TestDataQueryTestLineItemReq> testLines = reqObject.getTestLine();
        if (testLines != null) {
            testLines.forEach(tl -> {
                if (tl.getPpVersionId() > 0 && !ppMaps.containsKey(tl.getPpVersionId())) {
                    ppMaps.put(tl.getPpVersionId(), tl.getPpNo());
                }
            });
        }
        Set<String> slimCodes = Sets.newHashSet();
        Set<Integer> testLineIds = Sets.newHashSet();
        testDatas.forEach(td -> {
            td.setPpNo(ppMaps.get(td.getPpVersionId()));
            if (StringUtils.isNotBlank(td.getExternalCode())) {
                slimCodes.add(td.getExternalCode());
            }
            int testLineId = NumberUtil.toInt(td.getTestLineId());
            if (testLineId > 0) {
                testLineIds.add(testLineId);
            }
        });
        TestLineMappingInfoReq testLineMapping = new TestLineMappingInfoReq();
        testLineMapping.setProductLineCode(reqObject.getProductLineCode());
        testLineMapping.setLabCode(reqObject.getLabCode());
        testLineMapping.setSlimCodes(slimCodes);
        testLineMapping.setTestLineIds(testLineIds);

        // general
        Map<String, Integer> testLineMappingMaps = Maps.newHashMap();
        testLineMappingExtMapper.getTestLineMappingInfoList(testLineMapping).forEach(tlm -> {
            // SlimCode+PpNo+TestLineId+StandardId
            int ppNo = NumberUtil.toInt(tlm.getPpNo());
            String slimKey = this.getTestLineMappingKey(tlm.getSlimCode(), ppNo, tlm.getTestLineId(), tlm.getStandardId());
            if (StringUtils.isBlank(slimKey)) {
                return;
            }
            if (!testLineMappingMaps.containsKey(slimKey)) {
                testLineMappingMaps.put(slimKey, tlm.getId());
            }
        });

        if (testLineMappingMaps == null || testLineMappingMaps.isEmpty()) {
            return testLineAnalyteMappingMaps;
        }

        testDatas.forEach(td -> {
            td.setTestLineMappingId(this.getTestLineMappingId(td, testLineMappingMaps));
        });
        //Set<Integer> testLineMappingIds = testDatas.stream().filter(td -> NumberUtil.toInt(td.getTestLineMappingId()) > 0).map(td -> td.getTestLineMappingId()).collect(Collectors.toSet());
        Set<Integer> testLineMappingIds = Sets.newHashSet(testLineMappingMaps.values());
        if (testLineMappingIds == null || testLineMappingIds.isEmpty()) {
            return testLineAnalyteMappingMaps;
        }
        testLineAnalyteExtMapper.getTestLineAnalyteMappingList(testLineMappingIds, 1).forEach(mapping -> {
            if (StringUtils.isBlank(mapping.getAnalyteCode())) {
                return;
            }
            Integer testLineMappingId = mapping.getTestLineMappingId();
            if (!testLineAnalyteMappingMaps.containsKey(testLineMappingId)) {
                testLineAnalyteMappingMaps.put(testLineMappingId, Sets.newHashSet());
            }
            testLineAnalyteMappingMaps.get(testLineMappingId).add(mapping.getAnalyteCode().toUpperCase());
        });
        return testLineAnalyteMappingMaps;
    }


    public CustomResult<List<TestLineAnalyteMappingRsp>> getTestLineAnalyteMappingList() {
        CustomResult rspResult = new CustomResult();

        //testLineAnalyteExtMapper.getTestLineAnalyteMappingList


        return rspResult;
    }

    /**
     * @param td
     * @param testLineMappingMaps
     * @return
     */
    private int getTestLineMappingId(TestDataInfo td, Map<String, Integer> testLineMappingMaps) {
        int ppNo = NumberUtil.toInt(td.getPpNo());
        String slimKey = this.getTestLineMappingKey(td.getExternalCode(), ppNo, td.getTestLineId(), td.getCitationId());
        if (StringUtils.isBlank(slimKey)) {
            return 0;
        }
        int testLineMappingId = NumberUtil.toInt(testLineMappingMaps.get(slimKey));
        if (ppNo <= 0 || testLineMappingId > 0) {
            return testLineMappingId;
        }
        return NumberUtil.toInt(testLineMappingMaps.get(this.getTestLineMappingKey(td.getExternalCode(), 0, td.getTestLineId(), td.getCitationId())));
    }

    /**
     * @param slimCode
     * @param ppNo
     * @param testLineId
     * @param citationId
     * @return
     */
    private String getTestLineMappingKey(String slimCode, Integer ppNo, Integer testLineId, Integer citationId) {
        testLineId = NumberUtil.toInt(testLineId);
        if (StringUtils.isBlank(slimCode) || testLineId <= 0) {
            return null;
        }
        ppNo = NumberUtil.toInt(ppNo);
        return String.format("%s_%s_%s_%s", slimCode.toUpperCase(), ppNo, testLineId, NumberUtil.toInt(citationId));
    }

    /**
     * 删除 TestData数据
     *
     * @param reqObject
     * @return
     */
    public CustomResult deleteTestData(TestDataDeleteReq reqObject) {
        CustomResult rspResult = new CustomResult();
        if (reqObject == null || StringUtils.isBlank(reqObject.getObjectNo())) {
            return rspResult.fail("Parameter can't be null or subcontractNo can't be null");
        }
        String productLineCode = reqObject.getProductLineCode();
        String labCode = reqObject.getLabCode();
        if (StringUtils.isBlank(productLineCode) || StringUtils.isBlank(labCode)) {
            return rspResult.fail("Parameter productLineCode  or labCode can't be null");
        }
        String labString = this.getLabSuffixFromLabCode(labCode);
        reqObject.setLabCode(labString);

        testDataReportObjectRelExtMapper.deleteByObjectNoAndExternalNo(reqObject);
        rspResult.setSuccess(true);
        return rspResult;
    }

    /**
     * 根据labCode 解析出location ，查表用
     *
     * @param labCode
     * @return
     */
    public String getLabSuffixFromLabCode(String labCode) {
        String labString = StringUtils.EMPTY;
        if (StringUtils.isBlank(labCode)) {
            return labString;
        }
        if (!labCode.contains(" ")) {
            return labCode;
        }
        if (labCode.split(" ").length > 1) {
            labString = labCode.split(" ")[0].toLowerCase();
        }
        return labString.toLowerCase();
    }


    /**
     * @param reqObject
     * @return
     */
    public CustomResult cleanHistoryData(ConfigReq reqObject) {
        CustomResult rspResult = new CustomResult();

        if (CollectionUtils.isEmpty(reqObject.getLabCodes())) {
            return rspResult.fail("请检查参数labcodes");
        }
        List<String> labCodes = reqObject.getLabCodes();

        Integer sourceType = reqObject.getSourceType();
        String date = reqObject.getDate();
        String createDate = StringUtils.EMPTY;
        String endDate = StringUtils.EMPTY;
        String orderNo = reqObject.getOrderNo();
        if (StringUtils.isNotBlank(date)) {
            createDate = date.trim().split(",")[0];
            endDate = date.trim().split(",")[1];
        }
        for (String labCode : labCodes) {
            String[] s = labCode.split(" ");
            if (s.length != 2) {
                logger.info("请检查数据");
                continue;
            }
            String productLineCode = s[1];

            String suffix = StringUtil.getTestDataSuffix(labCode);

            // 获取当前Lab 的所有的mapping 关系
            List<TestDataMappingDTO> slimMappingInfos = configExtMapper.queryTestLineMappingByLabCode(labCode);

            Long lastId = 0L;
            do {
                logger.info("处理到tb_test_data_{}， id={}", suffix, lastId);
                List<OrderIdRelDTO> allOrderNoIdRel = configExtMapper.queryTestDataGroup(lastId, suffix, createDate, endDate, orderNo);
                if (CollectionUtils.isEmpty(allOrderNoIdRel)) {
                    break;
                }
                // 取最后一个orderNo的 maxId
                lastId = allOrderNoIdRel.get(allOrderNoIdRel.size() - 1).getMaxId();

                List<String> orderNos = allOrderNoIdRel.stream().map(OrderIdRelDTO::getOrderNo).collect(Collectors.toList());
                List<TestDataObjectRelPO> relPOs = Lists.newArrayList();
                List<TestDataMatrixInfoPO> testDataMatrixs = Lists.newArrayList();
                List<TestDataInfoPO> testDataInfos = Lists.newArrayList();

                // 获取订单下的 数据
                List<TestDataOldDTO> testDataOldDTOS = configExtMapper.queryTestData(orderNos, suffix);

                List<String> subcontractNos = testDataOldDTOS.stream().map(TestDataOldDTO::getObjectNo).distinct().collect(Collectors.toList());

                List<SubContractTestLineDTO> subContractTestLineDTOS = Lists.newArrayList();
                // 获取系统中的 testLine 数据
                List<SubContractTestLineDTO> response = subcontractClient.getSubContractTestLineInfo(subcontractNos, productLineCode);
                if (Func.isNotEmpty(response)) {
                    subContractTestLineDTOS = response;
                }

                // orderNo + objectNo + externalNo 为一条数据
                Map<String, List<TestDataOldDTO>> dataMaps = testDataOldDTOS.stream().collect(Collectors.groupingBy(item -> item.getOrderNo() + "-" + item.getObjectNo() + "-" + item.getExternalNo()));

                for (String dataKey : dataMaps.keySet()) {
                    List<TestDataOldDTO> testDatas = dataMaps.get(dataKey);
                    logger.info("处理到tb_test_data_{}， id={}, dataKey:{}", suffix, lastId, dataKey);

                    List<SubContractTestLineDTO> subTestLineDtos = subContractTestLineDTOS.stream().filter(item -> StringUtil.equalsIgnoreCase(item.getSubContractNo(), testDatas.get(0).getObjectNo())).collect(Collectors.toList());
                    // 补充 TestLine 信息
                    this.getTestDataSlimMappingResult(slimMappingInfos, subTestLineDtos, testDatas);

                    TestDataOldDTO testDataOldDTO = testDatas.get(0);

                    String userName = testDataOldDTO.getSystemId() == 1 ? "SLIM" : (testDataOldDTO.getSystemId() == 2 ? "FAST" : "system");

                    TestDataObjectRelPO relPO = new TestDataObjectRelPO();
                    relPO.setId(UUID.randomUUID().toString());
                    // objectNo + externalNo
                    TestDataObjectRelPO rel = new TestDataObjectRelPO();
                    rel.setObjectNo(testDataOldDTO.getObjectNo());
                    rel.setExternalNo(testDataOldDTO.getExternalNo());
                    rel.setSourceType(testDataOldDTO.getSystemId());
                    TestDataObjectRelPO reportObjectRel = objectRelExtMapper.getReportObjectRelInfo(rel);
                    if (reportObjectRel != null) {
                        relPO.setId(reportObjectRel.getId());
                    }
                    relPO.setProductLineCode(productLineCode);
                    relPO.setLabCode(labCode);
                    relPO.setObjectNo(testDataOldDTO.getObjectNo());
                    relPO.setOrderNo(testDataOldDTO.getOrderNo());
                    // 默认显示自己
                    relPO.setParentOrderNo(testDataOldDTO.getOrderNo());
                    // 获取订单相关信息
                    OrderSimplifyInfoRsp orderInfoByOrderNo = preOrderClient.getOrderSimplifyInfo(testDataOldDTO.getOrderNo(), productLineCode);
                    if (orderInfoByOrderNo != null) {
                        relPO.setReportNo(orderInfoByOrderNo.getReportNo());
                        if (OperationType.check(orderInfoByOrderNo.getOperationType(), OperationType.HostSubContract, OperationType.BindSubContract,
                                OperationType.LightSubContract, OperationType.ExecSubContract)) {
                            relPO.setParentOrderNo(orderInfoByOrderNo.getOldOrderNo());
                        }
                    }
                    relPO.setExternalId(null);
                    relPO.setExternalNo(testDataOldDTO.getExternalNo());
                    relPO.setSourceType(testDataOldDTO.getSystemId() == 1 ? SourceTypeEnum.SLIM.getCode() : (testDataOldDTO.getSystemId() == 2 ? SourceTypeEnum.FAST.getCode() : testDataOldDTO.getSystemId()));
                    relPO.setLanguageId(LanguageType.English.getLanguageId());

                    relPO.setBizVersionId(slimService.getTestDataReportObjectRelMd5(relPO));
                    relPO.setActiveIndicator(testDataOldDTO.getActiveIndicator() ? 1 : 0);
                    relPO.setCreatedBy(userName);
                    relPO.setCreatedDate(testDataOldDTO.getCreatedDate());
                    relPO.setModifiedBy(userName);
                    relPO.setModifiedDate(testDataOldDTO.getModifiedDate());

                    relPOs.add(relPO);

                    Map<String, List<TestDataOldDTO>> matrixMaps = testDatas.stream().filter(item -> StringUtil.isNotEmpty(item.getExternalCode()) && StringUtil.isNotEmpty(item.getExternalSampleNo()))
                            .collect(Collectors.groupingBy(item -> item.getExternalCode() + "_" + item.getTestLineId() + "_" + item.getCitationId() + "_" + item.getExternalSampleNo()));

                    // 获取 matrix 中的 <bizVersionId, id>关系
                    List<TestDataMatrixInfoPO> testDataMatrixInfoPOS = matrixInfoExtMapper.queryMatrix(relPO.getId(), suffix);
                    Map<String, Long> matrixBizMaps = Maps.newConcurrentMap();
                    if (!CollectionUtils.isEmpty(testDataMatrixInfoPOS)) {
                        matrixBizMaps = testDataMatrixInfoPOS.stream().collect(Collectors.toMap(TestDataMatrixInfoPO::getBizVersionId, TestDataMatrixInfoPO::getId, (k1, k2) -> k1));
                    }

                    for (String matrix : matrixMaps.keySet()) {
                        List<TestDataOldDTO> testDataItemReqs = matrixMaps.get(matrix);
                        TestDataMatrixInfoPO testDataMatrixInfoPO = new TestDataMatrixInfoPO();
                        TestDataOldDTO testDataItemReq = testDataItemReqs.get(0);

                        testDataMatrixInfoPO.setObjectRelId(relPO.getId());
                        testDataMatrixInfoPO.setExternalCode(testDataItemReq.getExternalCode());
                        testDataMatrixInfoPO.setTestLineId(testDataItemReq.getTestLineId());
                        testDataMatrixInfoPO.setCitationId(testDataItemReq.getCitationId());
                        testDataMatrixInfoPO.setCitationVersionId(testDataItemReq.getCitationVersionId());
                        testDataMatrixInfoPO.setSampleNo(testDataItemReq.getSampleNo());
                        testDataMatrixInfoPO.setExternalSampleNo(testDataItemReq.getExternalSampleNo());

                        TestDataMatrixExtFieldInfo testDataMatrixExtFieldInfo = new TestDataMatrixExtFieldInfo();
                        testDataMatrixExtFieldInfo.setMaterialName(testDataItemReq.getMaterialName());
                        testDataMatrixExtFieldInfo.setMaterialColor(testDataItemReq.getMaterialColor());
                        testDataMatrixExtFieldInfo.setMaterialTexture(testDataItemReq.getMaterialTexture());
                        testDataMatrixExtFieldInfo.setUsedPosition(testDataItemReq.getUsedPosition());
                        testDataMatrixInfoPO.setExtFields(JSONObject.toJSONString(testDataMatrixExtFieldInfo));

                        testDataMatrixInfoPO.setBizVersionId(slimService.getTestDataReportMatrixMd5(testDataMatrixInfoPO));
                        // 设置 Id
                        testDataMatrixInfoPO.setId(NumberUtil.defaultIfBlank(matrixBizMaps.get(testDataMatrixInfoPO.getBizVersionId()), idWorker.nextId()));

                        testDataMatrixInfoPO.setActiveIndicator(StatusEnum.VALID.getCode());
                        testDataMatrixInfoPO.setCreatedBy(userName);
                        testDataMatrixInfoPO.setCreatedDate(testDataOldDTO.getCreatedDate());
                        testDataMatrixInfoPO.setModifiedBy(userName);
                        testDataMatrixInfoPO.setModifiedDate(testDataOldDTO.getCreatedDate());
                        testDataMatrixs.add(testDataMatrixInfoPO);

                        for (TestDataOldDTO itemReq : testDataItemReqs) {
                            TestDataInfoPO testDataInfoPO = new TestDataInfoPO();

                            testDataInfoPO.setId(idWorker.nextId());
                            testDataInfoPO.setObjectRelId(relPO.getId());
                            testDataInfoPO.setTestDataMatrixId(testDataMatrixInfoPO.getId());
                            testDataInfoPO.setAnalyteName(itemReq.getTestAnalyteName());
                            testDataInfoPO.setAnalyteType(itemReq.getAnalyteType());
                            testDataInfoPO.setAnalyteCode(itemReq.getAnalyteCode());
                            testDataInfoPO.setAnalyteSeq(itemReq.getAnalyteSeq());
                            testDataInfoPO.setReportUnit(itemReq.getReportUnit());
                            testDataInfoPO.setTestValue(itemReq.getTestValue());
                            testDataInfoPO.setReportLimit(itemReq.getReportLimit());

                            List<SlimTestDataLangInfo> langInfos = Lists.newArrayList();
                            SlimTestDataLangInfo slimTestDataLangInfo = new SlimTestDataLangInfo();
                            slimTestDataLangInfo.setLanguageId(LanguageType.Chinese.getLanguageId());
                            slimTestDataLangInfo.setTestAnalyteName(itemReq.getTestAnalyteNameCN());
                            slimTestDataLangInfo.setReportUnit(itemReq.getReportUnitCN());
                            langInfos.add(slimTestDataLangInfo);
                            testDataInfoPO.setLanguages(JSONObject.toJSONString(langInfos));

                            testDataInfoPO.setBizVersionId(slimService.getTestDataReportMd5(testDataInfoPO));
                            testDataInfoPO.setActiveIndicator(StatusEnum.VALID.getCode());
                            testDataInfoPO.setCreatedBy(userName);
                            testDataInfoPO.setCreatedDate(testDataOldDTO.getCreatedDate());
                            testDataInfoPO.setModifiedBy(userName);
                            testDataInfoPO.setModifiedDate(testDataOldDTO.getCreatedDate());
                            testDataInfos.add(testDataInfoPO);
                        }
                    }
                }

                //分批插入
                List<List<TestDataObjectRelPO>> part = Lists.partition(relPOs, 50);
                for (List<TestDataObjectRelPO> testDataObjectRelPOS : part) {

                    // 相关数据线置为无效
                    Set<String> objectRelIds = testDataObjectRelPOS.stream().map(TestDataObjectRelPO::getId).collect(Collectors.toSet());
                    testDataReportMatrixExtMapper.updateInvalidOrValid(objectRelIds, suffix, "system");
                    testDataReporExtMapper.updateInvalidOrValid(objectRelIds, suffix, "system");

                    testDataReportObjectRelExtMapper.batchInsert(testDataObjectRelPOS);
                }

                //分批插入
                List<List<TestDataMatrixInfoPO>> part1 = Lists.partition(testDataMatrixs, 50);
                for (List<TestDataMatrixInfoPO> dataMatrixInfoPOS : part1) {

                    testDataReportMatrixExtMapper.batchInsert(dataMatrixInfoPOS, suffix);
                }

                //分批插入
                List<List<TestDataInfoPO>> part2 = Lists.partition(testDataInfos, 50);
                for (List<TestDataInfoPO> testDataInfoPOS : part2) {

                    testDataReporExtMapper.batchInsert(testDataInfoPOS, suffix);
                }

            } while (true);
            logger.info("处理{}完成！ tb_test_data_{}", labCode, suffix);

        }
        logger.info("已全部处理完成_{}", JSONObject.toJSONString(reqObject));


        rspResult.setSuccess(true);
        return rspResult;
    }


    /**
     * 获取订单中的相关数据 匹配mapping 关系
     *
     * @param slimMappingInfos
     * @param subContractTestLineDTOS
     */
    public void getTestDataSlimMappingResult(List<TestDataMappingDTO> slimMappingInfos, List<SubContractTestLineDTO> subContractTestLineDTOS, List<TestDataOldDTO> testDatas) {
        if (CollectionUtils.isEmpty(slimMappingInfos) || CollectionUtils.isEmpty(subContractTestLineDTOS)) {
            return;
        }

        Map<String, List<TestDataMappingDTO>> slimMappingMaps = slimMappingInfos.stream()
                .collect(Collectors.groupingBy(TestDataMappingDTO::getSlimCode));
        Map<Integer, List<Integer>> testLineInfoMaps = Maps.newHashMap();
        subContractTestLineDTOS.forEach(item -> {
            if (!testLineInfoMaps.containsKey(NumberUtil.toInt(item.getTestLineId()))) {
                testLineInfoMaps.put(NumberUtil.toInt(item.getTestLineId()), Lists.newArrayList(NumberUtil.toInt(item.getCitationId())));
            }
            List<Integer> citations = testLineInfoMaps.get(NumberUtil.toInt(item.getTestLineId()));
            if (citations.contains(NumberUtil.toInt(item.getCitationId()))) {
                return;
            }
            citations.add(NumberUtil.toInt(item.getCitationId()));
            testLineInfoMaps.put(NumberUtil.toInt(item.getTestLineId()), citations);
        });

        if (slimMappingMaps.isEmpty()) {
            return;
        }
        for (TestDataOldDTO item : testDatas) {
            if (!slimMappingMaps.containsKey(item.getExternalCode())
                    || CollectionUtils.isEmpty(slimMappingMaps.get(item.getExternalCode()))) {
                continue;
            }
            List<TestDataMappingDTO> slimMappings = slimMappingMaps.get(item.getExternalCode());
            Map<Integer, List<TestDataMappingDTO>> slimTestLineMap = slimMappings.stream()
                    .collect(Collectors.groupingBy(TestDataMappingDTO::getTestLineId));
            if (slimTestLineMap.isEmpty()) {
                continue;
            }
            for (Integer testLineItem : testLineInfoMaps.keySet()) {
                if (!slimTestLineMap.containsKey(testLineItem)) {
                    continue;
                }
                List<TestDataMappingDTO> testLineList = slimTestLineMap.get(testLineItem);
                if (CollectionUtils.isEmpty(testLineList)) {
                    continue;
                }
                List<Integer> citationIds = testLineInfoMaps.get(testLineItem);
                testLineList.forEach(testLineStand -> {
                    if (citationIds.contains(testLineStand.getStandardId())) {
                        item.setTestLineId(testLineStand.getTestLineId());
                        item.setCitationId(testLineStand.getStandardId());
//                        item.setCitationVersionId(testLineStand.getStandardVersionId());
                    }
                });
            }
        }


    }


    public CustomResult<List<TestDataTestMatrixRsp>> getTestDataInfoList(TestDataQueryReq reqObject) {
        CustomResult rspResult = new CustomResult();
        String productLineCode = reqObject.getProductLineCode();
        String labCode = reqObject.getLabCode();
        if (StringUtils.isBlank(productLineCode) || StringUtils.isBlank(labCode)) {
            return rspResult.fail("Parameter productLineCode or labCode can't be null");
        }
        if (StringUtils.isBlank(reqObject.getOrderNo())) {
            return rspResult.fail("Parameter orderNo can't be null");
        }
        if (CollectionUtils.isEmpty(reqObject.getSystemIds())) {
            return rspResult.fail("Parameter SystemId(1:slim   2:fast) can't be null");
        }
        Boolean transSchemeCode = reqObject.getTransSchemeCode();
        // 获取表明后缀
        String labString = this.getLabSuffixFromLabCode(labCode);
        // DIG-8246 slim 数据读取
        TestDataQueryReq req = new TestDataQueryReq();
        String orderNo = reqObject.getOrderNo();
        req.setLabCode(String.format("%s %s", labString.toUpperCase(), productLineCode));
        req.setOrderNo(reqObject.getOrderNo());
        req.setObjectNos(reqObject.getObjectNos());
        req.setSourceTypes(reqObject.getSystemIds());
        req.setProductLineCode(reqObject.getProductLineCode());
        CustomResult<List<TestDataInfo>> testDatasRsp = this.queryTestData(req);
        if (!testDatasRsp.isSuccess()) {
            // DIG-8533
            rspResult.setData(new ErrorRsp(ErrorEnum.Order));
            return rspResult;
        }

        List<TestDataInfo> testDatas = testDatasRsp.getData();

        Set<String> externalCodes = Sets.newConcurrentHashSet();
        Map<String, TestDataTestMatrixRsp> testMatrixMaps = Maps.newHashMap();

        testDatas.forEach(testData -> {
            TestDataConditionInfo testConditionInfo = this.getTestConditionInfo(testData.getTestConditions());
            if (testConditionInfo != null) {
                List<TestDataConditionLangInfo> languages = testConditionInfo.getLanguages();
                if (languages == null) {
                    languages = Lists.newLinkedList();
                }
                TestDataConditionLangInfo testConditionLang = languages.stream().findFirst().orElse(new TestDataConditionLangInfo());
                testData.setTestConditionDesc(StringUtil.getFirstVal(testConditionLang.getTestConditionName(), testConditionInfo.getTestConditionName()));
                testData.setTestConditionSeq(testConditionInfo.getTestConditionSeq());
                testData.setTestConditionId(testConditionInfo.getTestConditionId());
            }
        });

        List<TestDataConfigRsp> appendRuleData = this.getAppendRuleData(reqObject, testDatas);

        for (TestDataInfo testData : testDatas) {
            List<TestDataConditionInfo> testConditions = testData.getTestConditions();
            if (testConditions == null || testConditions.isEmpty()) {
                testConditions = Lists.newArrayList(new TestDataConditionInfo());
            }

            //copy from file-service :author QianWei
            if (transSchemeCode != null && transSchemeCode.booleanValue()) {
                testData.setReportUnit(AnalyteNameHelper.delHtmlTagV2(testData.getReportUnit()));
                testData.setReportUnitCN(AnalyteNameHelper.delHtmlTagV2(testData.getReportUnitCN()));
                testData.setTestAnalyteName(AnalyteNameHelper.delHtmlTagV2(testData.getTestAnalyteName()));
                testData.setTestAnalyteNameCN(AnalyteNameHelper.delHtmlTagV2(testData.getTestAnalyteNameCN()));
            }


            for (TestDataConditionInfo testCondition : testConditions) {
                int testConditionId = NumberUtil.toInt(testCondition.getTestConditionId());

                String testMatrixKey = String.format("%s_%s", testConditionId, testData.getTestDataMatrixId());
                TestDataTestMatrixRsp testMatrix = testMatrixMaps.get(testMatrixKey);
                if (testMatrix == null) {
                    testMatrix = new TestDataTestMatrixRsp();
                    testMatrix.setObjectNo(testData.getObjectNo());
                    testMatrix.setExternalNo(testData.getExternalNo());
                    testMatrix.setExternalCode(StringUtils.defaultString(testData.getExternalCode()));
                    testMatrix.setSampleNo(testData.getSampleNo());
                    testMatrix.setExternalSampleNo(testData.getExternalSampleNo());
                    testMatrix.setMaterialName(StringUtils.defaultString(testData.getMaterialName()));
                    testMatrix.setMaterialTexture(StringUtils.defaultString(testData.getMaterialTexture()));
                    testMatrix.setMaterialColor(StringUtils.defaultString(testData.getMaterialColor()));
                    testMatrix.setUsedPosition(StringUtils.defaultString(testData.getUsedPosition()));
                    testMatrix.setTestLineId(testData.getTestLineId());
                    testMatrix.setStandardId(testData.getCitationId());
                    testMatrix.setSystemId(testData.getSourceType());

                    if (NumberUtil.toInt(testData.getMatrixConclusionId()) > 0) {
                        TestDataConclusionRsp conclusion = new TestDataConclusionRsp();
                        conclusion.setConclusionId(testData.getMatrixConclusionId());
                        PriorityLevel priorityLevel = PriorityLevel.getLevel(testData.getMatrixConclusionId());
                        if (priorityLevel != null) {
                            conclusion.setConclusionCode(priorityLevel.getMessage());
                        }
                        conclusion.setCustomerConclusion(testData.getConclusionDisplay());

                        testMatrix.setConclusion(conclusion);
                    }
                    testMatrix.setTestResults(Lists.newLinkedList());
                }
                if (StringUtils.isNotBlank(testData.getExternalCode())) {
                    externalCodes.add(testData.getExternalCode());
                }
                List<TestDataTestResultRsp> testResults = testMatrix.getTestResults();

                TestDataTestResultRsp testResult = this.getTestDataResultInfo(orderNo, testData, testCondition, reqObject.getTransSchemeCode(), appendRuleData);
                if (testResult != null) {
                    testResults.add(testResult);
                }

                testMatrixMaps.put(testMatrixKey, testMatrix);
            }
        }
        List<TestDataQueryTestLineItemReq> testLines = reqObject.getTestLine();
        Collection<TestDataTestMatrixRsp> testMatrixs = testMatrixMaps.values();
        Collection<TestLineMappingDTO> testLineMappings = this.getTestLineMappingList(testLines, externalCodes, reqObject.getLabCode());
        // DIG-8555 A "NullPointerException" could be thrown; "testLineMappingMaps" is nullable here.
        if (Objects.isNull(testLines) || testLines.isEmpty() || Objects.isNull(testLineMappings) || testLineMappings.isEmpty()) {
            rspResult.setData(testMatrixs);
            rspResult.setSuccess(true);
            return rspResult;
        }

        testMatrixs.forEach(tm -> {
            if (StringUtils.isBlank(tm.getExternalCode())) {
                return;
            }
            testLineMappings.stream().filter(testLineMapping -> {
                return StringUtils.equalsIgnoreCase(testLineMapping.getSlimCode(), tm.getExternalCode());
            }).forEach(testLineMapping -> {
                testLines.stream().filter(tl -> NumberUtil.equals(tl.getTestLineId(), testLineMapping.getTestLineId())).forEach(testLine -> {
                    tm.setTestLineId(testLine.getTestLineId());
                    List<Integer> standardIds = testLine.getStandardIdList();
                    if (standardIds == null || standardIds.isEmpty()) {
                        return;
                    }
                    standardIds.forEach(standardId -> {
                        if (NumberUtil.equals(testLineMapping.getStandardId(), standardId)) {
                            tm.setStandardId(standardId);
                        }
                    });
                });
            });
        });

        rspResult.setData(testMatrixs);
        rspResult.setSuccess(true);
        return rspResult;
    }


    /**
     * @param testData
     * @param testCondition
     * @param isTransSchemeCode
     * @param appendRuleData
     * @return
     */
    private TestDataTestResultRsp getTestDataResultInfo(String orderNo, TestDataInfo testData, TestDataConditionInfo testCondition, boolean isTransSchemeCode, List<TestDataConfigRsp> appendRuleData) {
        if (testData == null || AnalyteTypeEnum.check(NumberUtil.toInt(testData.getAnalyteType()), AnalyteTypeEnum.Conclusion)) {
            return null;
        }
        TestDataTestResultRsp testResult = new TestDataTestResultRsp();
        testResult.setReportLimit(testData.getReportLimit());
        testResult.setAnalyteType(NumberUtil.toInt(testData.getAnalyteType()));

        Integer testConditionId = testCondition.getTestConditionId();
        Integer testLineId = testData.getTestLineId();
        TestDataConfigRsp testDataConfigRsp = appendRuleData.stream().filter(r -> NumberUtil.equals(testLineId, r.getTestLineId()) && NumberUtil.equals(testConditionId, r.getConditionId())).findFirst().orElse(null);
        TestDataConfigRuleInfo configRuleInfo = null;
        if (testDataConfigRsp != null) {
            String rule = testDataConfigRsp.getRule();
            try {
                configRuleInfo = JSONObject.parseObject(rule, TestDataConfigRuleInfo.class);
            } catch (Exception e) {
                logger.error("OrderNo:{} 转换配置数据异常，testLineId:{} conditionId:{}", orderNo, testLineId, testConditionId);
            }
        }

        String testResultNameCN = this.getTestResultName(testData, testCondition, configRuleInfo, LanguageType.Chinese);
        String testResultName = this.getTestResultName(testData, testCondition, configRuleInfo, LanguageType.English);

        testResult.setTestAnalyteNameCN(LOStringUtil.decode(testResultNameCN));
        testResult.setTestAnalyteName(LOStringUtil.decode(testResultName));

        testResult.setReportUnit(testData.getReportUnit());
        testResult.setReportUnitCN(StringUtils.defaultString(testData.getReportUnitCN()));
        testResult.setTestValue(testData.getTestValue());
        testResult.setAnalyteSeq(testData.getAnalyteSeq());

        return testResult;
    }

    private String getTestResultName(TestDataInfo testData, TestDataConditionInfo testCondition, TestDataConfigRuleInfo configRuleInfo, LanguageType languageType) {
        com.sgs.extsystem.facade.model.testdata.info.TestDataInfo td = new com.sgs.extsystem.facade.model.testdata.info.TestDataInfo();

        td.setTestConditionId(testCondition.getTestConditionId());
        td.setTestConditionDesc(testCondition.getTestConditionName());
        td.setTestConditionSeq(testCondition.getTestConditionSeq());

        List<TestDataConditionLangInfo> languages = testCondition.getLanguages();
        if (languages == null) {
            languages = Lists.newLinkedList();
        }
        TestDataConditionLangInfo lang = languages.stream().filter(language -> LanguageType.check(language.getLanguageId(), LanguageType.Chinese)).findFirst().orElse(null);
        if (lang != null) {
            td.setTestConditionDesc(lang.getTestConditionName());
        }
        td.setTestAnalyteName(testData.getTestAnalyteNameCN());
        td.setTestAnalyteCName(testData.getTestAnalyteNameCN());

        if (languageType == LanguageType.English) {
            td.setTestAnalyteCName(testData.getTestAnalyteName());
            td.setTestAnalyteName(testData.getTestAnalyteName());
        }

        return ExtStringUtil.joinAnalyteName(configRuleInfo, td, false);
    }


    private Collection<TestLineMappingDTO> getTestLineMappingList(List<TestDataQueryTestLineItemReq> testLines, Set<String> slimCodes, String labCode) {
        // Key(SlimCode_TestLineId)，Set<standardId>
        Map<String, TestLineMappingDTO> testLineMappingMaps = Maps.newHashMap();
        if (com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(testLines)) {
            return null;
        }
        testLineMappingExtMapper.queryTestLineMappingByLabCode(labCode, Lists.newArrayList(SourceTypeEnum.SLIM.getCode()), slimCodes)
                .forEach(testLineMapping -> {
                    int standardId = NumberUtil.toInt(testLineMapping.getStandardId());
                    if (StringUtils.isBlank(testLineMapping.getSlimCode()) || standardId <= 0) {
                        return;
                    }
                    String testLineMappingKey = String.format("%s_%s", NumberUtil.toInt(testLineMapping.getPpNo()), testLineMapping.getSlimCode(), testLineMapping.getTestLineId(), testLineMapping.getStandardId()).toLowerCase();
                    if (!testLineMappingMaps.containsKey(testLineMappingKey)) {
                        testLineMappingMaps.put(testLineMappingKey, testLineMapping);
                    }
                });
        return testLineMappingMaps.values();
    }

    private TestDataConditionInfo getTestConditionInfo(List<TestDataConditionInfo> testConditions) {
        if (testConditions == null || testConditions.isEmpty()) {
            return null;
        }
        TestDataConditionInfo testCondition = testConditions.stream().filter(tc -> tc.getLanguages() != null && tc.getLanguages().stream().filter(lang -> LanguageType.check(lang.getLanguageId(), LanguageType.Chinese)).count() > 0).findFirst().orElse(null);
        if (testCondition == null) {
            testCondition = testConditions.stream().findFirst().orElse(null);
        }
        return testCondition;
    }

    private List<TestDataConfigRsp> getAppendRuleData(TestDataQueryReq queryReq, List<TestDataInfo> testDatas) {
        String orderNo = queryReq.getOrderNo();
        String productLineCode = queryReq.getProductLineCode();
        CustomerSimplifyInfoReq customerSimplifyInfoReq = new CustomerSimplifyInfoReq();
        customerSimplifyInfoReq.setOrderNo(orderNo);
        customerSimplifyInfoReq.setProductLineCode(productLineCode);
        customerSimplifyInfoReq.setCustomerType("buyer");
        com.sgs.preorder.facade.model.common.BaseResponse<CustomerSimplifyInfoRsp> customerSimplifyInfoRsp = customerFacade.getCustomerSimplifyInfoByOrderId(customerSimplifyInfoReq);
        String customerGroupCode = null;
        if (customerSimplifyInfoRsp.getStatus() == ResponseCode.SUCCESS.getCode()) {
            CustomerSimplifyInfoRsp data = customerSimplifyInfoRsp.getData();
            customerGroupCode = data != null ? data.getCustomerGroupCode() : null;
        }
        List<Integer> testLineIds = testDatas.stream().map(t -> t.getTestLineId()).distinct().collect(Collectors.toList());
        List<Integer> testConditionIds = testDatas.stream().map(t -> t.getTestConditionId()).distinct().collect(Collectors.toList());
        TestDataConfigReq req = new TestDataConfigReq();
        req.setCustomerGroupCode(customerGroupCode);
        req.setTestLineIds(testLineIds);
        req.setConditionIds(testConditionIds);
        logger.info("orderNo:{} 查询testData配置：{}", orderNo, JSONObject.toJSONString(req));
        List<TestDataConfigRsp> list = queryTestDataConfig.queryTestDataConfig(req);
        logger.info("orderNo:{} 查询testData配置结果：{}", orderNo, JSONArray.toJSONString(list));
        return list;
    }
}
