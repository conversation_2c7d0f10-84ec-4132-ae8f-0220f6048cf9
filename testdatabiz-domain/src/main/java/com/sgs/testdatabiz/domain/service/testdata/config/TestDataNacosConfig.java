package com.sgs.testdatabiz.domain.service.testdata.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * TestData的配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "testdata.switch")
@RefreshScope
public class TestDataNacosConfig {

    private boolean testDataSaveSwitch; // 测试数据保存新旧逻辑开关
}
