package com.sgs.testdatabiz.domain.service.validation.config;

import java.util.Map;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import lombok.Data;

/**
 * 校验规则配置类
 * 用于从Nacos读取校验规则配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "validation.remote")
@RefreshScope
public class RemoteValidationRuleConfig {
    
    /**
     * 是否启用远程校验
     */
    private boolean enabled;

    /**
     * 校验规则配置
     */
    private Map<String, RemoteValidationRule> rules;
} 