package com.sgs.testdatabiz.domain.service.testdata.orchestrator;

import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataObjectRelPO;
import com.sgs.testdatabiz.domain.service.testdata.command.TestDataSaveCommand;
import com.sgs.testdatabiz.domain.service.testdata.exception.*;
import com.sgs.testdatabiz.domain.service.testdata.model.TestDataSuffix;
import com.sgs.testdatabiz.domain.service.testdata.repository.TestDataRepository;
import com.sgs.testdatabiz.domain.service.testdata.result.TestDataSaveResult;
import com.sgs.testdatabiz.domain.service.testdata.service.config.RepeatUpdateConfigService;
import com.sgs.testdatabiz.domain.service.testdata.service.matrix.TestMatrixService;
import com.sgs.testdatabiz.domain.service.testdata.service.objectrel.TestDataObjectRelService;
import com.sgs.testdatabiz.domain.service.testdata.service.testdata.TestDataService;
import com.sgs.testdatabiz.facade.model.testdata.ReportTestDataInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 测试数据保存编排器
 * 负责协调整个测试数据保存流程，按照适当的顺序调用各个领域服务
 * 
 * 主要职责：
 * 1. 协调配置检查、对象关系处理、矩阵构建和测试数据构建的整个流程
 * 2. 管理事务边界和错误处理
 * 3. 确保业务规则的正确执行顺序
 * 4. 提供统一的保存操作入口
 * 
 * <AUTHOR>
 */
@Service
public class TestDataSaveOrchestrator {

    private static final Logger logger = LoggerFactory.getLogger(TestDataSaveOrchestrator.class);

    @Autowired
    private RepeatUpdateConfigService repeatUpdateConfigService;

    @Autowired
    private TestDataObjectRelService testDataObjectRelService;

    @Autowired
    private TestMatrixService testMatrixService;

    @Autowired
    private TestDataService testDataService;

    @Autowired
    private TestDataRepository testDataRepository;

    @Autowired
    private TransactionTemplate transactionTemplate;

    /**
     * 编排测试数据保存操作
     * 按照业务流程的正确顺序协调各个领域服务
     * 
     * @param command 测试数据保存命令
     * @return 保存操作结果
     */
    public TestDataSaveResult orchestrateSave(TestDataSaveCommand command) {
        ReportTestDataInfo reportInfo = command.getReportTestDataInfo();
        String businessContext = String.format("reportNo=%s,orderNo=%s, objectNo=%s, externalNo=%s",
            reportInfo.getReportNo(),reportInfo.getOrderNo(), reportInfo.getSubContractNo(), reportInfo.getExternalNo());
        
        try {
            logger.info("开始编排测试数据保存操作，businessContext:{}", businessContext);

            // 第一步：处理对象关系
            List<TestDataObjectRelPO> objectRelations = processObjectRelations(reportInfo);
            if (CollectionUtils.isEmpty(objectRelations)) {
                throw ObjectRelationException.createObjectRelationCreationException(
                    reportInfo.getReportNo(), reportInfo.getSubContractNo(), reportInfo.getExternalNo());
            }

            // 获取主要的对象关系（用于后续处理）
            TestDataObjectRelPO mainObjectRel = getMainObjectRelation(objectRelations);

            // 第二步：生成数据表后缀
            TestDataSuffix suffix = TestDataSuffix.fromLabCode(reportInfo.getLabCode());
            logger.debug("生成的数据表后缀: {}", suffix.getValue());

            // 第三步：构建和协调测试矩阵
            List<TestDataMatrixInfoPO> testMatrices = processTestMatrices(reportInfo, mainObjectRel, suffix);

            // 第四步：构建和协调测试数据
            List<TestDataInfoPO> testDataList = processTestData(reportInfo, testMatrices, mainObjectRel, suffix);
            
            // 第五步：批量保存所有数据
            persistAllData(objectRelations, testMatrices, testDataList, suffix);

            logger.info("测试数据保存编排完成，businessContext: {}", businessContext);
            return TestDataSaveResult.success();

        } catch (TestDataSaveException e) {
            // 处理领域特定异常，保持原有错误消息
            logger.error("测试数据保存编排失败 - businessContext: {}", businessContext,e);
            return TestDataSaveResult.failure(e.getMessage());
        } catch (Exception e) {
            // 处理未预期的异常
            logger.error("测试数据保存编排发生未预期错误，业务上下文: {}", businessContext, e);
            TestDataSaveException wrappedException = new TestDataSaveException(
                "测试数据保存过程中发生未预期错误", e, "UNEXPECTED_ERROR", businessContext);
            return TestDataSaveResult.failure(wrappedException);
        }
    }

    /**
     * 处理对象关系
     * 包括原始对象关系处理和主对象关系创建
     */
    private List<TestDataObjectRelPO> processObjectRelations(ReportTestDataInfo reportInfo) {
        try {
            List<TestDataObjectRelPO> objectRelations = new ArrayList<>();

            // 处理原始对象关系（如果存在originalReportNo）
            TestDataObjectRelPO originalObjectRel = testDataObjectRelService.handleOriginalObjectRelation(reportInfo);
            if (originalObjectRel != null) {
                objectRelations.add(originalObjectRel);
                logger.debug("添加原始对象关系，originalReportNo: {}", reportInfo.getOriginalReportNo());
            }

            // 创建主对象关系
            TestDataObjectRelPO mainObjectRel = testDataObjectRelService.createObjectRelation(reportInfo);

            // 检查是否已存在相同的对象关系
            TestDataObjectRelPO existedObjectRel = testDataObjectRelService.findExistingObjectRelation(mainObjectRel);
            if (existedObjectRel != null) {
                // 检查重复更新权限
                boolean canRepeatUpdate = testDataObjectRelService.canRepeatUpdate(existedObjectRel, reportInfo);
                if (!canRepeatUpdate) {
                    // 抛出配置异常，保持原有错误消息
                    String identityId = getIdentityIdBySourceType(reportInfo.getSourceType());
                    throw ConfigurationException.createRepeatUpdateException(
                        identityId, reportInfo.getProductLineCode(), "2");
                }

                // 更新对象关系ID
                mainObjectRel = testDataObjectRelService.updateWithExistingId(mainObjectRel, existedObjectRel);
                logger.debug("更新已存在的对象关系，ID: {}", existedObjectRel.getId());
            }

            objectRelations.add(mainObjectRel);
            return objectRelations;
            
        } catch (TestDataSaveException e) {
            // 重新抛出领域异常
            throw e;
        } catch (Exception e) {
            // 包装为对象关系异常
            throw ObjectRelationException.createObjectRelationQueryException(
                reportInfo.getReportNo(), reportInfo.getSubContractNo(), reportInfo.getExternalNo(), e);
        }
    }
    
    /**
     * 根据源类型获取标识ID
     */
    private String getIdentityIdBySourceType(int sourceType) {
        // 根据原始代码的逻辑
        if (sourceType == 30) { // StarLims
            return "30";
        } else if (sourceType == 31) { // SLim
            return "31";
        }
        return String.valueOf(sourceType);
    }

    /**
     * 获取主要的对象关系（非原始对象关系）
     */
    private TestDataObjectRelPO getMainObjectRelation(List<TestDataObjectRelPO> objectRelations) {
        // 主对象关系通常是列表中的最后一个（非原始对象关系）
        // 优化：根据对象关系的activeIndicator字段来判断主对象关系，而不是简单地取最后一个元素
        return objectRelations.stream()
                .filter(rel -> rel.getActiveIndicator() != null && rel.getActiveIndicator() == 1)
                .findFirst()
                .orElse(objectRelations.get(objectRelations.size() - 1));
    }

    /**
     * 处理测试矩阵
     * 包括构建矩阵、创建映射和与现有数据协调
     */
    private List<TestDataMatrixInfoPO> processTestMatrices(ReportTestDataInfo reportInfo,
            TestDataObjectRelPO objectRel,
            TestDataSuffix suffix) {
        try {
            // 构建测试矩阵
            List<TestDataMatrixInfoPO> testMatrices = testMatrixService.buildTestMatrices(reportInfo, objectRel);
            logger.debug("构建了 {} 个测试矩阵", testMatrices.size());

            // 处理重复矩阵的特殊情况
            Map<String, TestDataMatrixInfoPO> matrixMap = testMatrixService.handleDuplicateMatrices(testMatrices);

            // 与现有矩阵数据进行协调
            testMatrixService.reconcileWithExistingMatrices(matrixMap, objectRel.getId(), suffix.getValue());

            // 返回协调后的矩阵列表
            return new ArrayList<>(matrixMap.values());
            
        } catch (TestDataSaveException e) {
            // 重新抛出领域异常
            throw e;
        } catch (Exception e) {
            // 包装为数据协调异常
            String businessContext = String.format("reportNo=%s,orderNo=%s,objectRelId=%s, suffix=%s, matrixCount=%d",
                    reportInfo.getReportNo(),reportInfo.getOrderNo(),objectRel.getId(), suffix.getValue(), reportInfo.getTestMatrixs() != null ? reportInfo.getTestMatrixs().size() : 0);
            logger.error("测试矩阵处理发生未预期错误，businessContext: {}", businessContext, e);
            throw DataReconciliationException.createDataBuildException("TestMatrix", e, businessContext);
        }
    }

    /**
     * 处理测试数据
     * 包括构建测试数据和与现有数据协调
     */
    private List<TestDataInfoPO> processTestData(ReportTestDataInfo reportInfo,
            List<TestDataMatrixInfoPO> testMatrices,
            TestDataObjectRelPO objectRel,
            TestDataSuffix suffix) {
        try {
            // 构建测试数据
            List<TestDataInfoPO> testDataList = testDataService.buildTestDataList(
                    reportInfo.getTestMatrixs(), testMatrices);
            logger.debug("构建了 {} 条测试数据", testDataList.size());

            // 与现有测试数据进行协调
            List<TestDataInfoPO> reconciledTestData = testDataService.reconcileWithExistingTestData(
                    testDataList, objectRel.getId(), suffix.getValue());

            return reconciledTestData;
            
        } catch (TestDataSaveException e) {
            // 重新抛出领域异常
            throw e;
        } catch (Exception e) {
            // 包装为数据协调异常
            String businessContext = String.format("objectRelId=%s, suffix=%s, matrixCount=%d", 
                objectRel.getId(), suffix.getValue(), testMatrices.size());
            throw DataReconciliationException.createDataBuildException("TestData", e, businessContext);
        }
    }

    /**
     * 持久化所有数据
     * 按照正确的顺序保存对象关系、测试矩阵和测试数据
     */
    private TestDataSaveResult persistAllData(List<TestDataObjectRelPO> objectRelations,
            List<TestDataMatrixInfoPO> testMatrices,
            List<TestDataInfoPO> testDataList,
            TestDataSuffix suffix) {
            //开启事务，如果执行时报，打印日志并且回滚，返回结果

            return transactionTemplate.execute((tranStatus) -> {
                try {
                    // 1. 保存对象关系
                    if (!CollectionUtils.isEmpty(objectRelations)) {
                        Integer objectRelCount = testDataRepository.batchInsertObjectRelations(objectRelations);
                        if (objectRelCount == null || objectRelCount <= 0) {
                            String businessContext = String.format("expectedRows=%d, actualRows=%d", 
                                objectRelations.size(), objectRelCount != null ? objectRelCount : 0);
                            throw DataPersistenceException.createBatchInsertException(
                                "test_data_object_rel", objectRelations.size(), objectRelCount != null ? objectRelCount : 0, businessContext);
                        }
                        logger.debug("保存了 {} 个对象关系", objectRelCount);
                    }

                    // 2. 保存测试矩阵
                    if (!CollectionUtils.isEmpty(testMatrices)) {
                        Integer matrixCount = testDataRepository.batchInsertMatrices(testMatrices, suffix.getValue());
                        if (matrixCount == null || matrixCount < 0) {
                            String businessContext = String.format("expectedRows=%d, actualRows=%d, suffix=%s", 
                                testMatrices.size(), matrixCount != null ? matrixCount : 0, suffix.getValue());
                            throw DataPersistenceException.createBatchInsertException(
                                "test_data_matrix_info_" + suffix.getValue(), testMatrices.size(), matrixCount != null ? matrixCount : 0, businessContext);
                        }
                        logger.debug("保存了 {} 个测试矩阵", matrixCount);
                    }

                    // 3. 保存测试数据
                    if (!CollectionUtils.isEmpty(testDataList)) {
                        Integer testDataCount = testDataRepository.batchInsertTestData(testDataList, suffix.getValue());
                        if (testDataCount == null || testDataCount < 0) {
                            String businessContext = String.format("expectedRows=%d, actualRows=%d, suffix=%s", 
                                testDataList.size(), testDataCount != null ? testDataCount : 0, suffix.getValue());
                            throw DataPersistenceException.createBatchInsertException(
                                "test_data_info_" + suffix.getValue(), testDataList.size(), testDataCount != null ? testDataCount : 0, businessContext);
                        }
                        logger.debug("保存了 {} 条测试数据", testDataCount);
                    }
                    return TestDataSaveResult.success();
                } catch (Exception e) {
                    tranStatus.setRollbackOnly(); // 回滚事务
                    logger.error("测试数据保存编排失败 - {}: {}", e.getClass().getSimpleName(), e.getMessage());
                    return TestDataSaveResult.failure(e.getMessage());
                }
            });
    }
}