package com.sgs.testdatabiz.domain.service;

import com.sgs.framework.core.base.CustomResult;
import com.sgs.testdatabiz.dbstorages.mybatis.mapper.testdata.SlimConfigExtMapper;
import com.sgs.testdatabiz.facade.model.req.SlimConfigReq;
import com.sgs.testdatabiz.facade.model.rsp.SlimConfigRsp;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/6/28 9:47
 */
@Service
public class FtpCfgService {

    @Autowired
    private SlimConfigExtMapper slimConfigExtMapper;

    /**
     * 查询所有的ftp配置。不区分BU
     * @return
     */
    public List<SlimConfigRsp> getAllFTPConfig() {
        SlimConfigReq slimConfigReq = new SlimConfigReq();
        List<SlimConfigRsp> slimConfigRsps = slimConfigExtMapper.getConfInfoList(slimConfigReq);
        if(slimConfigRsps==null){
            slimConfigRsps = new ArrayList<>();
        }
        return slimConfigRsps;
    }

    /**
     * 通过productLineCode 和 labCode(非必须) 查询配置
     * @param req
     * @return
     */
    public CustomResult<List<SlimConfigRsp>> queryConfig(SlimConfigReq req) {
        CustomResult<List<SlimConfigRsp>> result = new CustomResult<>();
        if(req == null || StringUtils.isBlank(req.getProductLineCode())){
            return result.fail("Parameter can't be null or productLineCode can't be null");
        }
        List<SlimConfigRsp> list =  slimConfigExtMapper.getConfInfoList(req);
        result.setSuccess(true);
        result.setData(list);
        return result  ;
    }
}
