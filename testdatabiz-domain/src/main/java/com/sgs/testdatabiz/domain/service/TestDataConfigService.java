package com.sgs.testdatabiz.domain.service;

import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.ConfigExtMapper;
import com.sgs.testdatabiz.facade.model.req.TestDataConfigReq;
import com.sgs.testdatabiz.facade.model.rsp.TestDataConfigRsp;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @ClassName TestDataConfigService
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/5/6
 */
@Service
public class TestDataConfigService {
    @Autowired
    private ConfigExtMapper configExtMapper;

    /**
     * 查询general 和 具体数据配置
     * @param req
     * @return
     */
    public List<TestDataConfigRsp> queryTestDataConfig(TestDataConfigReq req) {
        //参数全为空，直接按照general的查询
        if(req!=null &&
                StringUtils.isBlank(req.getCustomerGroupCode())
                && CollectionUtils.isEmpty(req.getTestLineIds())
                && CollectionUtils.isEmpty(req.getCitationIds())
                && Func.isEmpty(req.getLanguageId())
                && CollectionUtils.isEmpty(req.getConditionIds()) ){
            req = null;
        }
        List<TestDataConfigRsp> generalList = configExtMapper.queryTestDataConfig(null);
        if(req == null){
            return generalList;
        }
        List<TestDataConfigRsp> list = configExtMapper.queryTestDataConfig(req);
        return CollectionUtils.isNotEmpty(list) ? list :generalList;
    }
}
