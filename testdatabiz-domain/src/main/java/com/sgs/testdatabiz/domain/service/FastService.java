package com.sgs.testdatabiz.domain.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.model.enums.SourceTypeEnum;
import com.sgs.framework.model.enums.TestDataSystem;
import com.sgs.otsnotes.facade.model.enums.CitationType;
import com.sgs.otsnotes.facade.model.enums.LanguageType;
import com.sgs.testdatabiz.core.annotation.SourceType;
import com.sgs.testdatabiz.core.util.DateUtils;
import com.sgs.testdatabiz.core.util.SnowflakeIdWorker;
import com.sgs.testdatabiz.core.util.StringUtil;
import com.sgs.testdatabiz.core.util.Transcoding;
import com.sgs.testdatabiz.dbstorages.mybatis.enums.ActiveIndicatorEnum;
import com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.TestDataInfoExtMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.TestDataMatrixInfoExtMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.TestDataObjectRelExtMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.extmodel.TestDataDTO;
import com.sgs.testdatabiz.dbstorages.mybatis.extmodel.TestDataObjectRelDTO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataObjectRelPO;
import com.sgs.testdatabiz.domain.service.testdata.impl.check.TableNameChecker;
import com.sgs.testdatabiz.facade.model.info.starlims.TestDataLangInfo;
import com.sgs.testdatabiz.facade.model.req.fast.TestDataInfoReq;
import com.sgs.testdatabiz.facade.model.req.fast.TestLineTestDataReq;
import com.sgs.testdatabiz.facade.model.req.fast.TestLineTestResultReq;
import com.sgs.testdatabiz.facade.model.req.fast.TestResultLangReq;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;

@Service
@SourceType(sourceType = SourceTypeEnum.FAST)
public class FastService extends AbstractTestDataService<TestDataInfoReq> {

    private static final Logger logger = LoggerFactory.getLogger(FastService.class);

    @Autowired
    private TestDataObjectRelExtMapper testDataReportObjectRelExtMapper;
    @Autowired
    private TestDataMatrixInfoExtMapper testDataReportMatrixExtMapper;
    @Autowired
    private TestDataInfoExtMapper testDataReporExtMapper;
    @Autowired
    private SnowflakeIdWorker idWorker;

    @Autowired
    private TableNameChecker tableNameChecker;
    @Override
    protected CustomResult<TestDataDTO> doInvoke(TestDataInfoReq reqObject) {
        CustomResult<TestDataDTO> rspResult = new CustomResult();
        List<TestDataMatrixInfoPO> matrixs = Lists.newArrayList();
        TestDataDTO testDataDTO = new TestDataDTO();
        List<TestDataInfoPO> testDatas = Lists.newArrayList();
        List<TestDataInfoPO> testDataReports = Lists.newArrayList();
        List<TestDataObjectRelPO> objectRelPOS = Lists.newArrayList();
        String fastName = SourceTypeEnum.FAST.getDesc();
        Date now = DateUtils.getNow();
        String objectRelId = String.valueOf(idWorker.nextId());
        String suffix = StringUtil.getTestDataSuffix(reqObject.getLabCode());

        tableNameChecker.checkTableNameExist(reqObject.getLabCode());

        TestDataObjectRelPO rel = new TestDataObjectRelPO();
        rel.setReportNo(reqObject.getReportNo());
        rel.setObjectNo(reqObject.getObjectNo());
        rel.setExternalNo(reqObject.getJobNo());
        rel.setSourceType(SourceTypeEnum.FAST.getCode());

        // 查看数据库是否有相关数据已经回传 - retest场景下，测试数据可重新回传更新
        TestDataObjectRelPO reportObjectRel= testDataReportObjectRelExtMapper.getReportObjectRelInfo(rel);
        // 构建testData关联关系
        TestDataObjectRelPO testDataObjectRel = getTestDataObjectRel(reqObject, fastName, now, objectRelId);
        if (reportObjectRel != null) {
            objectRelId = reportObjectRel.getId();
            testDataObjectRel.setId(objectRelId);
        }
        objectRelPOS.add(testDataObjectRel);

        Map<String, TestDataMatrixInfoPO> testDataMatrixMaps = Maps.newHashMap();
        testDataReportMatrixExtMapper.queryMatrix(testDataObjectRel.getId(), suffix).forEach(testDataMatrix->{
            testDataMatrixMaps.put(testDataMatrix.getBizVersionId(), testDataMatrix);
        });

        Map<String, TestDataInfoPO> testDataMaps = Maps.newHashMap();
        testDataReporExtMapper.queryTestDataInfo(testDataObjectRel.getId(), suffix).forEach(testData->{
            testDataMaps.put(testData.getBizVersionId(), testData);
        });

        List<TestLineTestDataReq> datas = reqObject.getDatas();
        for (TestLineTestDataReq testData : datas) {
            if (CollectionUtils.isEmpty(testData.getTestResults()) || CollectionUtils.isEmpty(testData.getTestLineIds())) {
                continue;
            }
            // 同一个testData下每个testLineId下的testResult都是一样的 和 Fast处约定如此
            for (Integer testLineId : testData.getTestLineIds()) {
                // 构建Matrix
                TestDataMatrixInfoPO testDataMatrix = getTestDataMatrixInfo(fastName, now, objectRelId, testData, testLineId);
                // 查看Matrix是否为更新操作
                TestDataMatrixInfoPO oldTestDataMatrix = testDataMatrixMaps.get(testDataMatrix.getBizVersionId());
                if (oldTestDataMatrix != null){
                    Long testDataMatrixId = oldTestDataMatrix.getId();
                    // 更新ID
                    testDataMatrix.setId(testDataMatrixId);
                    testDataMatrixMaps.remove(oldTestDataMatrix.getBizVersionId());
                }
                matrixs.add(testDataMatrix);
//                testDatas.forEach(td -> td.setTestDataMatrixId(testDataMatrix.getId()));

                for (TestLineTestResultReq testResult : testData.getTestResults()) {
                    TestDataInfoPO testDataInfo = getTestDataInfoPO(fastName, now, objectRelId, testDataMatrix.getId(), testResult);
                    testDataInfo.setTestDataMatrixId(testDataMatrix.getId());

                    // 查看testData是否为更新操作
                    TestDataInfoPO oldTestData = testDataMaps.get(testDataInfo.getBizVersionId());
                    if (oldTestData != null){
                        Long testDataId = oldTestData.getId();
                        // 更新ID
                        testDataInfo.setId(testDataId);
                        testDataMaps.remove(oldTestData.getBizVersionId());
                    }
                    testDatas.add(testDataInfo);
                }
                CopyOnWriteArrayList<TestDataInfoPO> testDataInfoPOS = Lists.newCopyOnWriteArrayList(testDatas);
                testDataReports.addAll(testDataInfoPOS);
            }
        }
        testDataDTO.setTestDataObjectRels(objectRelPOS);
        testDataDTO.setTestDataInfos(testDataReports);
        testDataDTO.setTestDataMatrixInfos(matrixs);
        testDataDTO.setTestDataSuffix(StringUtil.getTestDataSuffix(reqObject.getLabCode()));
        TestDataObjectRelDTO objectRelDTO = new TestDataObjectRelDTO();
        objectRelDTO.setObjectNo(reqObject.getObjectNo());
        objectRelDTO.setReportNo(reqObject.getReportNo());
        objectRelDTO.setModifiedDate(now);
        objectRelDTO.setModifiedBy(fastName);
        testDataDTO.setObjectRelDTO(objectRelDTO);
        CustomResult dealResult = this.doDeal(testDataDTO);
        rspResult.setSuccess(dealResult.isSuccess());
        if (!rspResult.isSuccess()) {
            rspResult.setMsg(dealResult.getMsg());
        }
        return rspResult;
    }

    @NotNull
    private TestDataInfoPO getTestDataInfoPO(String fastName, Date now, String objectRelId, Long testDataMatrixId, TestLineTestResultReq testResult) {
        TestDataInfoPO testDataInfo = new TestDataInfoPO();

        testDataInfo.setId(idWorker.nextId());
        testDataInfo.setObjectRelId(objectRelId);
        testDataInfo.setTestDataMatrixId(testDataMatrixId);
        testDataInfo.setAnalyteCode(testResult.getAnalyte());
        testDataInfo.setAnalyteType(0);//General
        testDataInfo.setAnalyteName(testResult.getAnalyteAlias());
        testDataInfo.setAnalyteSeq(testResult.getSorter());
        testDataInfo.setReportUnit(testResult.getUnit());
        testDataInfo.setTestValue(testResult.getResult());
        List<TestResultLangReq> languages = testResult.getLanguages();
        List<TestDataLangInfo> langs = Lists.newArrayList();
        for (TestResultLangReq language : languages) {
            TestDataLangInfo lang = new TestDataLangInfo();
            lang.setLanguageId(language.getLanguageId());
            lang.setAnalyteAlias(language.getAnalyteAlias());
            String analyte = language.getAnalyte();
            String analyteName = Transcoding.unicodeToChar(analyte);
            if (StringUtil.isNotEmpty(analyteName)) {
                lang.setTestAnalyteName(analyteName);
            }else {
                lang.setTestAnalyteName(analyte);
            }
            langs.add(lang);
        }
        if (!langs.isEmpty()){
            testDataInfo.setLanguages(JSONObject.toJSONString(langs));
        }
        testDataInfo.setCreatedBy(fastName);
        testDataInfo.setCreatedDate(now);
        testDataInfo.setModifiedBy(fastName);
        testDataInfo.setModifiedDate(now);
        testDataInfo.setActiveIndicator(ActiveIndicatorEnum.ACTIVE.getValue());
        testDataInfo.setBizVersionId(this.getTestDataReportMd5(testDataInfo));
        return testDataInfo;
    }

    @NotNull
    private TestDataMatrixInfoPO getTestDataMatrixInfo(String fastName, Date now, String objectRelId, TestLineTestDataReq testData, Integer testLineId) {
        TestDataMatrixInfoPO testDataReportMatrixPO = new TestDataMatrixInfoPO();
        // fast场景下externalCode和testLineId应该存储一样的值，因为Fast无externalCode字段
        testDataReportMatrixPO.setTestLineId(testLineId);
        testDataReportMatrixPO.setCitationType(CitationType.None.getType());
        testDataReportMatrixPO.setObjectRelId(objectRelId);
        testDataReportMatrixPO.setExternalCode(String.valueOf(testLineId));
        testDataReportMatrixPO.setSampleNo(testData.getSampleNo());
        testDataReportMatrixPO.setSampleId(testData.getSampleId());
        testDataReportMatrixPO.setBizVersionId(this.getTestDataReportMatrixMd5(testDataReportMatrixPO));
        testDataReportMatrixPO.setCreatedBy(fastName);
        testDataReportMatrixPO.setCreatedDate(now);
        testDataReportMatrixPO.setModifiedBy(fastName);
        testDataReportMatrixPO.setModifiedDate(now);
        testDataReportMatrixPO.setActiveIndicator(ActiveIndicatorEnum.ACTIVE.getValue());
        testDataReportMatrixPO.setId(idWorker.nextId());
        return testDataReportMatrixPO;
    }

    @NotNull
    private TestDataObjectRelPO getTestDataObjectRel(TestDataInfoReq reqObject, String fastName, Date now, String objectRelId) {
        TestDataObjectRelPO testDataObjectRelPO = new TestDataObjectRelPO();
        testDataObjectRelPO.setId(objectRelId);
        testDataObjectRelPO.setProductLineCode(reqObject.getProductLineCode());
        testDataObjectRelPO.setLabCode(reqObject.getLabCode());
        testDataObjectRelPO.setOrderNo(reqObject.getOrderNo());//tb_test_data_object_rel.orderNo
        testDataObjectRelPO.setReportNo(reqObject.getReportNo());
        testDataObjectRelPO.setObjectNo(reqObject.getJobNo());//tb_test_data_object_rel.objectNo=JobNo
        testDataObjectRelPO.setExternalNo(reqObject.getJobNo());//tb_test_data_object_rel.externalNo=JobNo
        testDataObjectRelPO.setSourceType(SourceTypeEnum.FAST.getCode());//1、slim 2、job 3、starlims 4、fast 5、subcontract 6、new
        testDataObjectRelPO.setLanguageId(LanguageType.EN.getLanguageId());//fast默认报告都是英文
        testDataObjectRelPO.setCompleteDate(reqObject.getCompletedDate());
        testDataObjectRelPO.setCreatedBy(fastName);
        testDataObjectRelPO.setCreatedDate(now);
        testDataObjectRelPO.setModifiedBy(fastName);
        testDataObjectRelPO.setModifiedDate(now);
        testDataObjectRelPO.setActiveIndicator(ActiveIndicatorEnum.ACTIVE.getValue());//0: inactive, 1: active
        testDataObjectRelPO.setBizVersionId(this.getTestDataReportObjectRelMd5(testDataObjectRelPO));
        return testDataObjectRelPO;
    }


}
