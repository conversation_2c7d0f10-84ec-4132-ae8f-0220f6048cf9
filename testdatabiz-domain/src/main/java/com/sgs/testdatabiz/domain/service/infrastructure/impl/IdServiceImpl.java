package com.sgs.testdatabiz.domain.service.infrastructure.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import com.sgs.testdatabiz.domain.service.infrastructure.api.IdService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class IdServiceImpl implements IdService {
    private static final long DATA_CENTER_ID_BITS = 5L;
    private static final long MAX_DATA_CENTER_ID = -1L ^ (-1L << DATA_CENTER_ID_BITS);

    private static final long WORKER_ID_BITS = 5L;

    private static final long MAX_WORKER_ID = -1L ^ (-1L << WORKER_ID_BITS);
    private static Snowflake snowflake;

    static {
        log.info("初始化id生成器 ");

        /**
         * 注意不要改动日期
         * id生成器纪元时间
         */
        String startTime = "2023-03-21 00:00:00";
        long dataCenterId = IdUtil.getDataCenterId(MAX_DATA_CENTER_ID);
        log.info("id generator， dataCenterId : {}", dataCenterId);
        long workerId = IdUtil.getWorkerId(dataCenterId, MAX_WORKER_ID);
        log.info("id generator，workerId : {}", workerId);
        snowflake = new Snowflake(DateUtil.parse(startTime), workerId, dataCenterId, true);
    }


    @Override
    public long nextId() {
        return snowflake.nextId();
    }
}
