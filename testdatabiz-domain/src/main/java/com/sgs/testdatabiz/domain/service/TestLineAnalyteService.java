package com.sgs.testdatabiz.domain.service;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.core.util.DateUtils;
import com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.TestLineAnalyteExtMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestLineAnalyteMappingInfoPO;
import com.sgs.testdatabiz.facade.model.req.TestLineAnalyteMappingReq;
import com.sgs.testdatabiz.facade.model.rsp.TestLineAnalyteMappingRsp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
public class TestLineAnalyteService {
    private static final Logger logger = LoggerFactory.getLogger(TestLineAnalyteService.class);
    @Autowired
    private TestLineAnalyteExtMapper testLineAnalyteExtMapper;

    /**
     *
     * @param reqObject
     * @return
     */
    public CustomResult batchInsert(TestLineAnalyteMappingReq reqObject) {
        CustomResult rspResult = new CustomResult();
        Set<Integer> testLineMappingIds = reqObject.getTestLineMappingIds();
        if (testLineMappingIds == null || testLineMappingIds.size() > 1){
            return rspResult.fail("TestLineMappingId 不能超过多条.");
        }
        Set<String> analyteCodes = reqObject.getAnalyteCodes();
        if (analyteCodes == null){
            analyteCodes = Sets.newHashSet();
        }
        Integer testLineMappingId = testLineMappingIds.stream().findFirst().orElse(null);
        List<TestLineAnalyteMappingRsp> testLineAnalytes = testLineAnalyteExtMapper.getTestLineAnalyteMappingList(testLineMappingIds,null);

        Map<String, TestLineAnalyteMappingInfoPO> testLineAnalyteMaps = Maps.newHashMap();
        testLineAnalytes.forEach(analyte->{
            TestLineAnalyteMappingInfoPO testLineAnalyte = new TestLineAnalyteMappingInfoPO();
            BeanUtils.copyProperties(analyte, testLineAnalyte);
            // 0无效，1有效
            testLineAnalyte.setStatus(0);
            testLineAnalyte.setCreatedBy(reqObject.getRegionAccount());
            testLineAnalyte.setCreatedDate(DateUtils.getNow());
            testLineAnalyte.setModifiedBy(reqObject.getRegionAccount());
            testLineAnalyte.setModifiedDate(DateUtils.getNow());
            testLineAnalyteMaps.put(analyte.getAnalyteCode(), testLineAnalyte);
        });
        for (String analyteCode: analyteCodes) {
            TestLineAnalyteMappingInfoPO testLineAnalyte = testLineAnalyteMaps.get(analyteCode);
            if (testLineAnalyte != null){
                // 0无效，1有效
                testLineAnalyte.setStatus(1);
                continue;
            }
            testLineAnalyte = new TestLineAnalyteMappingInfoPO();
            testLineAnalyte.setTestLineMappingId(testLineMappingId);
            testLineAnalyte.setAnalyteCode(analyteCode);
            testLineAnalyte.setStatus(1);
            testLineAnalyte.setCreatedBy(reqObject.getRegionAccount());
            testLineAnalyte.setCreatedDate(DateUtils.getNow());
            testLineAnalyte.setModifiedBy(reqObject.getRegionAccount());
            testLineAnalyte.setModifiedDate(DateUtils.getNow());

            testLineAnalyteMaps.put(analyteCode, testLineAnalyte);
        }
        if (testLineAnalyteMaps.isEmpty()){
            rspResult.setSuccess(true);
            return rspResult;
        }
        rspResult.setSuccess(testLineAnalyteExtMapper.batchInsert(testLineAnalyteMaps.values()) > 0);
        return rspResult;
    }

    /**
     *
     * @param reqObject
     * @return
     */
    public CustomResult<List<TestLineAnalyteMappingRsp>> getTestLineAnalyteInfoList(TestLineAnalyteMappingReq reqObject) {
        CustomResult rspResult = new CustomResult();
        Set<Integer> testLineMappingIds = reqObject.getTestLineMappingIds();
        if (testLineMappingIds == null || testLineMappingIds.isEmpty()){
            return rspResult.fail("TestLineMappingId 无效.");
        }
        long count = testLineMappingIds.stream().filter(Func::isNotEmpty).count();
        if (count == 0) {
            return rspResult.fail("TestLineMappingId 无效.");
        }
        List<TestLineAnalyteMappingRsp> testLineAnalytes = testLineAnalyteExtMapper.getTestLineAnalyteMappingList(testLineMappingIds, reqObject.getStatus());
        rspResult.setData(testLineAnalytes);
        rspResult.setSuccess(true);
        return rspResult;
    }
}
