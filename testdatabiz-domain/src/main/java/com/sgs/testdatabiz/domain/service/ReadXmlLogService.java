package com.sgs.testdatabiz.domain.service;

import com.sgs.framework.model.enums.ProductLineType;
import com.sgs.framework.model.enums.SourceTypeEnum;
import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.core.util.DateUtils;
import com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.ReadXmlLogExtMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.model.ReadXmlLogPO;
import com.sgs.testdatabiz.domain.service.utils.SourceTypeUtils;
import com.sgs.testdatabiz.domain.service.utils.TestDataImportContext;
import com.sgs.testdatabiz.facade.model.testdata.ReportTestDataInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 *
 */
@Service
public class ReadXmlLogService{
    private final Logger logger = LoggerFactory.getLogger(ReadXmlLogService.class);
    @Autowired
    private ReadXmlLogExtMapper readXmlLogExtMapper;

    @Async
    public void asyncBatchSaveLog(ReportTestDataInfo reportTestData, String errMsg){
        if (StringUtils.isBlank(errMsg)){
            logger.info("[batchSaveLog] errorMsgList is empty.");
            return;
        }

        // 获得当前渠道
        SourceTypeEnum sourceTypeEnum = TestDataImportContext.getChannel();
        if (sourceTypeEnum ==null && reportTestData !=null){
            sourceTypeEnum = SourceTypeUtils.toSourceTypeEnum(reportTestData.getSourceType());
        }
        if (sourceTypeEnum ==null){
            logger.error("[batchSaveLog] sourceType is null");
            return;
        }

        // SCI-133 去除slim保存的错误信息，错误信息统一在fileService处记录
        if (sourceTypeEnum ==SourceTypeEnum.SLIM){
            logger.info("[batchSaveLog] with no need for slim channel.");
            return;
        }

        if (Func.isEmpty(reportTestData)) {
            logger.info("[batchSaveLog] reportTestData cannot null.");
            return;
        }

        // 2.组成ReadXmlLogPO
        ReadXmlLogPO errLog = new ReadXmlLogPO();
        if (reportTestData != null) {
            // set ProductLineId
            ProductLineType productLineType = ProductLineType.findProductLineAbbr(reportTestData.getProductLineCode());
            if (productLineType != null) {
                errLog.setProductLineId(productLineType.getProductLineId());
            }
            errLog.setOrderNo(reportTestData.getOrderNo());
            errLog.setSubcontractNo(reportTestData.getSubContractNo());
            errLog.setSlimJobNo(reportTestData.getExternalNo());
        }
        // TODO Yaofeng 该值怎么取呢？？？
        errLog.setLabId(null);
        errLog.setFileName(null);
        errLog.setNewFileName(null);
        // TODO Yaofeng 这个需要从File Service LogCodeEnum.TOSLIMTESTRESULT(20012,"%s"),
        errLog.setLogCode(20012);
        errLog.setSubReportNo(reportTestData.getExternalObjectNo());
        errLog.setLogRecord(StringUtils.substring(errMsg, 0, 500));
        errLog.setSystemId(sourceTypeEnum.getCode());
        errLog.setCreatedDate(DateUtils.getNow());
        errLog.setModifiedDate(DateUtils.getNow());

        readXmlLogExtMapper.insert(errLog);

        return;
    }
}
