package com.sgs.testdatabiz.domain.service.validation.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Set;

@Data
@Component
@ConfigurationProperties(prefix = "validation.exclude")
@RefreshScope
public class ValidationExcludeConfig {
    
    private boolean excludeEnabled = false;  // 排除功能默认关闭
    private Map<String, ExcludeStrategy> excludeStrategies;
    
    @Data
    public static class ExcludeStrategy {
        private boolean strategyEnabled = false;  // 排除策略默认关闭
        private Set<String> excludedSystemIds;  // 需要排除校验的系统ID列表
    }
} 