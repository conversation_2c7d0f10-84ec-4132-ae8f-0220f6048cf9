package com.sgs.testdatabiz.domain.service.testdata.repository;

import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.TestDataInfoExtMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.TestDataMatrixInfoExtMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.extmapper.testdata.TestDataObjectRelExtMapper;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataInfoPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataMatrixInfoPO;
import com.sgs.testdatabiz.dbstorages.mybatis.model.TestDataObjectRelPO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

/**
 * 测试数据仓储实现类
 * 封装底层Mapper调用，提供统一的数据访问接口
 * 包含适当的错误处理和日志记录
 * 
 * <AUTHOR>
 */
@Repository
public class TestDataRepositoryImpl implements TestDataRepository {

    private static final Logger logger = LoggerFactory.getLogger(TestDataRepositoryImpl.class);

    @Autowired
    private TestDataObjectRelExtMapper testDataObjectRelExtMapper;

    @Autowired
    private TestDataMatrixInfoExtMapper testDataMatrixInfoExtMapper;

    @Autowired
    private TestDataInfoExtMapper testDataInfoExtMapper;

    // ==================== TestDataObjectRel 相关操作 ====================

    @Override
    public Integer batchInsertObjectRelations(List<TestDataObjectRelPO> objectRels) {
        if (objectRels == null || objectRels.isEmpty()) {
            logger.warn("批量插入对象关系失败：对象关系列表为空");
            return 0;
        }

        try {
            logger.debug("开始批量插入对象关系，数量: {}", objectRels.size());
            
            Integer result = testDataObjectRelExtMapper.batchInsert(objectRels);
            
            if (result == null || result <= 0) {
                String errorMsg = String.format("批量插入对象关系失败，期望插入 %d 条记录，实际插入 %d 条", 
                        objectRels.size(), result);
                logger.error(errorMsg);
                throw new RuntimeException(errorMsg);
            }

            logger.info("批量插入对象关系成功，插入记录数: {}", result);
            return result;

        } catch (Exception e) {
            String errorMsg = String.format("批量插入对象关系时发生异常，记录数: %d", objectRels.size());
            logger.error(errorMsg, e);
            throw new RuntimeException(errorMsg, e);
        }
    }

    @Override
    public TestDataObjectRelPO findObjectRelation(TestDataObjectRelPO objectRel) {
        if (objectRel == null) {
            logger.warn("查找对象关系失败：查询条件为空");
            return null;
        }

        try {
            logger.debug("查找对象关系，报告编号: {}, 对象编号: {}, 外部编号: {}",
                    objectRel.getReportNo(), objectRel.getObjectNo(), objectRel.getExternalNo());

            TestDataObjectRelPO result = testDataObjectRelExtMapper.getReportObjectRelInfo(objectRel);

            if (result != null) {
                logger.debug("找到对象关系，ID: {}", result.getId());
            } else {
                logger.debug("未找到匹配的对象关系");
            }

            return result;

        } catch (Exception e) {
            String errorMsg = String.format("查找对象关系时发生异常，报告编号: %s, 对象编号: %s, 外部编号: %s",
                    objectRel.getReportNo(), objectRel.getObjectNo(), objectRel.getExternalNo());
            logger.error(errorMsg, e);
            throw new RuntimeException(errorMsg, e);
        }
    }

    // ==================== TestDataMatrix 相关操作 ====================

    @Override
    public Integer batchInsertMatrices(List<TestDataMatrixInfoPO> testMatrices, String suffix) {
        if (testMatrices == null || testMatrices.isEmpty()) {
            logger.debug("批量插入测试矩阵：矩阵列表为空，跳过插入操作");
            return 0;
        }

        if (Func.isBlank(suffix)) {
            logger.warn("批量插入测试矩阵失败：表后缀为空");
            throw new IllegalArgumentException("表后缀不能为空");
        }

        try {
            logger.debug("开始批量插入测试矩阵，数量: {}, 表后缀: {}", testMatrices.size(), suffix);

            Integer result = testDataMatrixInfoExtMapper.batchInsert(testMatrices, suffix);

            logger.info("批量插入测试矩阵完成，插入记录数: {}, 表后缀: {}", result, suffix);
            return result;

        } catch (Exception e) {
            String errorMsg = String.format("批量插入测试矩阵时发生异常，记录数: %d, 表后缀: %s", 
                    testMatrices.size(), suffix);
            logger.error(errorMsg, e);
            throw new RuntimeException(errorMsg, e);
        }
    }

    @Override
    public List<TestDataMatrixInfoPO> findExistingMatrices(String objectRelId, String suffix) {
        if (Func.isBlank(objectRelId)) {
            logger.warn("查找现有测试矩阵失败：对象关系ID为空");
            return Collections.emptyList();
        }

        try {
            logger.debug("查找现有测试矩阵，对象关系ID: {}, 表后缀: {}", objectRelId, suffix);

            List<TestDataMatrixInfoPO> result = testDataMatrixInfoExtMapper.queryMatrix(objectRelId, suffix);

            if (result == null) {
                result = Collections.emptyList();
            }

            logger.debug("查找到现有测试矩阵数量: {}", result.size());
            return result;

        } catch (Exception e) {
            String errorMsg = String.format("查找现有测试矩阵时发生异常，对象关系ID: %s, 表后缀: %s", 
                    objectRelId, suffix);
            logger.error(errorMsg, e);
            throw new RuntimeException(errorMsg, e);
        }
    }

    // ==================== TestData 相关操作 ====================

    @Override
    public Integer batchInsertTestData(List<TestDataInfoPO> testData, String suffix) {
        if (testData == null || testData.isEmpty()) {
            logger.debug("批量插入测试数据：测试数据列表为空，跳过插入操作");
            return 0;
        }

        if (Func.isBlank(suffix)) {
            logger.warn("批量插入测试数据失败：表后缀为空");
            throw new IllegalArgumentException("表后缀不能为空");
        }

        try {
            logger.debug("开始批量插入测试数据，数量: {}, 表后缀: {}", testData.size(), suffix);

            Integer result = testDataInfoExtMapper.batchInsert(testData, suffix);

            logger.info("批量插入测试数据完成，插入记录数: {}, 表后缀: {}", result, suffix);
            return result;

        } catch (Exception e) {
            String errorMsg = String.format("批量插入测试数据时发生异常，记录数: %d, 表后缀: %s", 
                    testData.size(), suffix);
            logger.error(errorMsg, e);
            throw new RuntimeException(errorMsg, e);
        }
    }

    @Override
    public List<TestDataInfoPO> findExistingTestData(String objectRelId, String suffix) {
        if (Func.isBlank(objectRelId)) {
            logger.warn("查找现有测试数据失败：对象关系ID为空");
            return Collections.emptyList();
        }

        try {
            logger.debug("查找现有测试数据，对象关系ID: {}, 表后缀: {}", objectRelId, suffix);

            List<TestDataInfoPO> result = testDataInfoExtMapper.queryTestDataInfo(objectRelId, suffix);

            if (result == null) {
                result = Collections.emptyList();
            }

            logger.debug("查找到现有测试数据数量: {}", result.size());
            return result;

        } catch (Exception e) {
            String errorMsg = String.format("查找现有测试数据时发生异常，对象关系ID: %s, 表后缀: %s", 
                    objectRelId, suffix);
            logger.error(errorMsg, e);
            throw new RuntimeException(errorMsg, e);
        }
    }
}