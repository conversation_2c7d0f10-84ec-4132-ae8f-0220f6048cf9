package com.sgs.testdatabiz.domain.util;

import com.sgs.testdatabiz.core.exception.ReportDataCheckException;
import com.sgs.testdatabiz.facade.model.req.subcontract.SubcontractQueryReq;
import com.sgs.testdatabiz.facade.model.req.subcontract.SubcontractReplaceReq;
import com.sgs.testdatabiz.facade.model.req.subcontract.SubcontractInvalidateReq;
import org.junit.Test;

/**
 * 分包数据参数校验工具类测试
 * 
 * 测试分包数据管理相关接口的参数校验功能
 * 验证各种正常和异常场景下的校验逻辑
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
public class SubcontractValidationUtilTest {

    /**
     * 测试查询请求参数校验 - 正常情况
     */
    @Test
    public void testValidateQueryRequest_Success() {
        SubcontractQueryReq req = new SubcontractQueryReq();
        req.setOrderNo("ORD-2024-001");
        req.setLabCode("GZ");
        req.setReportNo("RPT-2024-001");
        req.setObjectNo("SC-2024-001"); // 可选字段
        
        // 应该不抛出异常
        SubcontractValidationUtil.validateQueryRequest(req);
    }

    /**
     * 测试查询请求参数校验 - 请求对象为空
     */
    @Test(expected = ReportDataCheckException.class)
    public void testValidateQueryRequest_NullRequest() {
        SubcontractValidationUtil.validateQueryRequest(null);
    }

    /**
     * 测试查询请求参数校验 - 订单号为空
     */
    @Test(expected = ReportDataCheckException.class)
    public void testValidateQueryRequest_EmptyOrderNo() {
        SubcontractQueryReq req = new SubcontractQueryReq();
        req.setOrderNo("");
        req.setLabCode("GZ");
        req.setReportNo("RPT-2024-001");
        
        SubcontractValidationUtil.validateQueryRequest(req);
    }

    /**
     * 测试替换请求参数校验 - 正常情况
     */
    @Test
    public void testValidateReplaceRequest_Success() {
        SubcontractReplaceReq req = new SubcontractReplaceReq();
        req.setOrderNo("ORD-2024-001");
        req.setLabCode("GZ");
        req.setReportNo("RPT-2024-001");
        req.setNewReportNo("RPT-2024-002");
        
        // 应该不抛出异常
        SubcontractValidationUtil.validateReplaceRequest(req);
    }

    /**
     * 测试替换请求参数校验 - 新旧报告号相同
     */
    @Test(expected = ReportDataCheckException.class)
    public void testValidateReplaceRequest_SameReportNo() {
        SubcontractReplaceReq req = new SubcontractReplaceReq();
        req.setOrderNo("ORD-2024-001");
        req.setLabCode("GZ");
        req.setReportNo("RPT-2024-001");
        req.setNewReportNo("RPT-2024-001"); // 与原报告号相同
        
        SubcontractValidationUtil.validateReplaceRequest(req);
    }

    /**
     * 测试无效化请求参数校验 - 正常情况
     */
    @Test
    public void testValidateInvalidateRequest_Success() {
        SubcontractInvalidateReq req = new SubcontractInvalidateReq();
        req.setOrderNo("ORD-2024-001");
        req.setObjectNo("SC-2024-001");
        req.setLabCode("GZ");
        req.setReportNo("RPT-2024-001");
        
        // 应该不抛出异常
        SubcontractValidationUtil.validateInvalidateRequest(req);
    }

    /**
     * 测试无效化请求参数校验 - 请求对象为空
     */
    @Test(expected = ReportDataCheckException.class)
    public void testValidateInvalidateRequest_NullRequest() {
        SubcontractValidationUtil.validateInvalidateRequest(null);
    }

    /**
     * 测试无效化请求参数校验 - 实验室代码为空
     */
    @Test(expected = ReportDataCheckException.class)
    public void testValidateInvalidateRequest_EmptyLabCode() {
        SubcontractInvalidateReq req = new SubcontractInvalidateReq();
        req.setOrderNo("ORD-2024-001");
        req.setObjectNo("SC-2024-001");
        req.setLabCode(""); // 空字符串
        req.setReportNo("RPT-2024-001");
        
        SubcontractValidationUtil.validateInvalidateRequest(req);
    }
}
