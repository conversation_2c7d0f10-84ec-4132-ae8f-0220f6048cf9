package com.sgs.testdatabiz.facade.impl;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.model.enums.SourceTypeEnum;
import com.sgs.testdatabiz.domain.service.EnterSubContractService;
import com.sgs.testdatabiz.domain.service.SourceDataStrategyFactory;
import com.sgs.testdatabiz.facade.SubContractFacade;
import com.sgs.testdatabiz.facade.model.req.entersubcontract.SubTestDataReq;
import com.sgs.testdatabiz.facade.model.req.backsubcontract.SubCompleteTestDataReq;
import com.sgs.testdatabiz.facade.model.req.entersubcontract.InputStreamReq;
import com.sgs.testdatabiz.facade.model.req.entersubcontract.SubcontractNoReq;
import com.sgs.testdatabiz.facade.model.req.subcontract.SubcontractQueryReq;
import com.sgs.testdatabiz.facade.model.req.subcontract.SubcontractReplaceReq;
import com.sgs.testdatabiz.facade.model.req.subcontract.SubcontractInvalidateReq;
import com.sgs.testdatabiz.facade.model.rsp.subcontract.ReportDataRsp;
import com.sgs.testdatabiz.facade.model.testdata.ReportTestDataInfo;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("testDataSubContractFacade")
public class SubContractFacadeImpl implements SubContractFacade {

    @Autowired
    private EnterSubContractService enterSubContractService;
    @Autowired
    private SourceDataStrategyFactory sourceDataStrategyFactory;

//    @Override
//    public BaseResponse<XSSFWorkbook> downLoadTemplate(SubcontractNoReq reqObject) {
//        return BaseResponse.newInstance(enterSubContractService.downLoadTemplate(reqObject));
//    }
//
//    @Override
//    public BaseResponse uploadSubTemplate(InputStreamReq reqObject) {
//        return BaseResponse.newInstance(sourceDataStrategyFactory.doInvoke(reqObject, SourceTypeEnum.ENTERSUBCONTRACT));
//    }

    @Override
    public BaseResponse<ReportDataRsp> querySubTestData(SubcontractNoReq reqObject) {
        return BaseResponse.newInstance(enterSubContractService.querySubTestData(reqObject));
    }

    @Override
    public BaseResponse saveSubCompleteTestData(SubCompleteTestDataReq reqObject) {
        return BaseResponse.newInstance(sourceDataStrategyFactory.doInvoke(reqObject, SourceTypeEnum.SUBCONTRACT));
//        return BaseResponse.newInstance(CustomResult.newSuccessInstance());
    }

    @Override
    public BaseResponse cancelSubTestData(SubcontractNoReq reqObject) {
        return BaseResponse.newInstance(enterSubContractService.cancelSubTestData(reqObject));
    }

    @Override
    public BaseResponse saveEnterSubContractTestData(SubTestDataReq reqObject) {
        return BaseResponse.newInstance(enterSubContractService.doInvoke(reqObject));
    }

    /**
     * 查询分包数据
     */
    @Override
    public BaseResponse<List<ReportTestDataInfo>> querySubcontractData(SubcontractQueryReq req) {
        return BaseResponse.newInstance(enterSubContractService.querySubcontractData(req));
    }

    /**
     * 替换分包数据ReportNo
     */
    @Override
    public BaseResponse<Void> replaceSubcontractReportNo(SubcontractReplaceReq req) {
        return BaseResponse.newInstance(enterSubContractService.replaceSubcontractReportNo(req));
    }

    /**
     * 设置分包数据为无效状态
     */
    @Override
    public BaseResponse<Void> invalidateSubcontractData(SubcontractInvalidateReq req) {
        return BaseResponse.newInstance(enterSubContractService.invalidateSubcontractData(req));
    }


}
