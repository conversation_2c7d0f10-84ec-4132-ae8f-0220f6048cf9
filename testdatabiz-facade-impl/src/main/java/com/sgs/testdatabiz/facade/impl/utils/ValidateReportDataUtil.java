package com.sgs.testdatabiz.facade.impl.utils;

import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.facade.model.dto.rd.report.ReportDataBatchDTO;
import com.sgs.testdatabiz.facade.model.req.rd.ImportQuotationReq;
import com.sgs.testdatabiz.facade.model.req.rd.ImportReportInvoiceReq;
import com.sgs.testdatabiz.integration.model.validation.ValidationRequest;
import org.jetbrains.annotations.NotNull;

public class ValidateReportDataUtil {

    private static final String OPERATOR_EMAIL = "<EMAIL>";
    private static final String OPERATOR = "operator";
    private static final String VALIDATE_LEVEL =  "testing";
    private static final String VALIDATE_MODE = "SyncCompleted";
    public static @NotNull ValidationRequest getValidationRequestByReportDataBatchDTO(ReportDataBatchDTO reportDataBatchDTO) {
        ValidationRequest reqs = new ValidationRequest();
        reqs.setData(reportDataBatchDTO);
        reqs.setMode(VALIDATE_MODE);
        reqs.setBuId(reportDataBatchDTO.getBuId());
        reqs.setBuCode(reportDataBatchDTO.getBuCode());
        reqs.setLabId(reportDataBatchDTO.getLabId());
        reqs.setLabCode(reportDataBatchDTO.getLabCode());
        reqs.setSystemId(reportDataBatchDTO.getSystemId());
        reqs.setRequestId(reportDataBatchDTO.getRequestId());
        if(Func.isNotEmpty(reportDataBatchDTO.getTrfList())){
            reqs.setRefSystemId(reportDataBatchDTO.getTrfList().get(0).getRefSystemId());
        }
        reqs.setSourceProductLineCode(reportDataBatchDTO.getSourceProductLineCode());
        reqs.setProductLineCode(reportDataBatchDTO.getProductLineCode());
        reqs.setValidateLevel(VALIDATE_LEVEL);
        reqs.setOperator(OPERATOR);

        //当前请求时间
        //reqs.setOperationTime(OffsetDateTime.now().toString());
        reqs.setProductLineCode(reportDataBatchDTO.getProductLineCode());
        reqs.setOperatorEmail(OPERATOR_EMAIL);
        if(Func.notNull(reportDataBatchDTO.getTrfList())&&reportDataBatchDTO.getTrfList().size() > 1){
            reqs.setTrfNo(reportDataBatchDTO.getTrfList().get(0).getTrfNo());
        }
        reqs.setValidateName("reportdata");
        return reqs;
    }

    public static ValidationRequest getValidationRequestByImportReportInvoiceReq(ImportReportInvoiceReq request) {
        ValidationRequest reqs = new ValidationRequest();
        reqs.setData(request);
        reqs.setMode(VALIDATE_MODE);
        reqs.setBuId(request.getBuId());
        reqs.setBuCode(request.getBuCode());
        reqs.setLabId(request.getLabId());
        reqs.setLabCode(request.getLabCode());
        reqs.setSystemId(request.getSystemId());
        reqs.setRequestId(request.getRequestId());
        reqs.setValidateLevel(VALIDATE_LEVEL);
        reqs.setOperator(OPERATOR);
        reqs.setSourceProductLineCode(request.getSourceProductLineCode());
        reqs.setProductLineCode(request.getProductLineCode());
        reqs.setValidateName("invoice");
        return reqs;
    }

    public static ValidationRequest getValidationRequestByImportReportInvoiceReq(ImportQuotationReq request) {
        ValidationRequest reqs = new ValidationRequest();
        reqs.setData(request);
        reqs.setMode(VALIDATE_MODE);
        reqs.setBuId(request.getBuId());
        reqs.setBuCode(request.getBuCode());
        reqs.setLabId(request.getLabId());
        reqs.setLabCode(request.getLabCode());
        reqs.setSystemId(request.getSystemId());
        reqs.setRequestId(request.getRequestId());
        reqs.setValidateLevel(VALIDATE_LEVEL);
        reqs.setOperator(OPERATOR);
        reqs.setSourceProductLineCode(request.getSourceProductLineCode());
        reqs.setProductLineCode(request.getProductLineCode());
        reqs.setValidateName("quotation");
        return reqs;
    }
}
