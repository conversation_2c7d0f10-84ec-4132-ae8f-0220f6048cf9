package com.sgs.testdatabiz.test;

import com.sgs.testdatabiz.core.exception.ReportDataCheckException;
import com.sgs.testdatabiz.core.util.SubcontractValidationUtil;
import com.sgs.testdatabiz.facade.model.req.subcontract.SubcontractQueryReq;
import com.sgs.testdatabiz.facade.model.req.subcontract.SubcontractReplaceReq;
import com.sgs.testdatabiz.facade.model.req.subcontract.SubcontractInvalidateReq;

/**
 * 分包数据参数校验功能测试类
 * 
 * 验证SubcontractValidationUtil工具类的参数校验功能
 * 包括正常情况和各种异常情况的测试
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
public class SubcontractValidationTest {

    /**
     * 测试主方法
     */
    public static void main(String[] args) {
        System.out.println("开始分包数据参数校验功能测试...");
        
        // 测试查询请求校验
        testQueryRequestValidation();
        
        // 测试替换请求校验
        testReplaceRequestValidation();
        
        // 测试无效化请求校验
        testInvalidateRequestValidation();
        
        System.out.println("分包数据参数校验功能测试完成!");
    }

    /**
     * 测试查询请求参数校验
     */
    private static void testQueryRequestValidation() {
        System.out.println("\n=== 测试查询请求参数校验 ===");
        
        // 1. 测试正常情况
        try {
            SubcontractQueryReq validReq = new SubcontractQueryReq();
            validReq.setOrderNo("ORD-2024-001");
            validReq.setObjectNo("SC-2024-001");
            validReq.setLabCode("GZ");
            validReq.setReportNo("RPT-2024-001");
            
            SubcontractValidationUtil.validateQueryRequest(validReq);
            System.out.println("✓ 正常参数校验通过");
        } catch (Exception e) {
            System.out.println("✗ 正常参数校验失败: " + e.getMessage());
        }
        
        // 2. 测试请求对象为空
        try {
            SubcontractValidationUtil.validateQueryRequest(null);
            System.out.println("✗ 请求对象为空校验失败");
        } catch (ReportDataCheckException e) {
            System.out.println("✓ 请求对象为空校验通过: " + e.getMessage());
        }
        
        // 3. 测试订单号为空
        try {
            SubcontractQueryReq req = new SubcontractQueryReq();
            req.setOrderNo("");
            req.setObjectNo("SC-2024-001");
            req.setLabCode("GZ");
            req.setReportNo("RPT-2024-001");
            
            SubcontractValidationUtil.validateQueryRequest(req);
            System.out.println("✗ 订单号为空校验失败");
        } catch (ReportDataCheckException e) {
            System.out.println("✓ 订单号为空校验通过: " + e.getMessage());
        }
        
        // 4. 测试对象编号为空
        try {
            SubcontractQueryReq req = new SubcontractQueryReq();
            req.setOrderNo("ORD-2024-001");
            req.setObjectNo("");
            req.setLabCode("GZ");
            req.setReportNo("RPT-2024-001");
            
            SubcontractValidationUtil.validateQueryRequest(req);
            System.out.println("✗ 对象编号为空校验失败");
        } catch (ReportDataCheckException e) {
            System.out.println("✓ 对象编号为空校验通过: " + e.getMessage());
        }
        
        // 5. 测试实验室代码为空
        try {
            SubcontractQueryReq req = new SubcontractQueryReq();
            req.setOrderNo("ORD-2024-001");
            req.setObjectNo("SC-2024-001");
            req.setLabCode("");
            req.setReportNo("RPT-2024-001");
            
            SubcontractValidationUtil.validateQueryRequest(req);
            System.out.println("✗ 实验室代码为空校验失败");
        } catch (ReportDataCheckException e) {
            System.out.println("✓ 实验室代码为空校验通过: " + e.getMessage());
        }
        
        // 6. 测试报告号为空
        try {
            SubcontractQueryReq req = new SubcontractQueryReq();
            req.setOrderNo("ORD-2024-001");
            req.setObjectNo("SC-2024-001");
            req.setLabCode("GZ");
            req.setReportNo("");
            
            SubcontractValidationUtil.validateQueryRequest(req);
            System.out.println("✗ 报告号为空校验失败");
        } catch (ReportDataCheckException e) {
            System.out.println("✓ 报告号为空校验通过: " + e.getMessage());
        }
    }

    /**
     * 测试替换请求参数校验
     */
    private static void testReplaceRequestValidation() {
        System.out.println("\n=== 测试替换请求参数校验 ===");
        
        // 1. 测试正常情况
        try {
            SubcontractReplaceReq validReq = new SubcontractReplaceReq();
            validReq.setOrderNo("ORD-2024-001");
            validReq.setLabCode("GZ");
            validReq.setReportNo("RPT-2024-001");
            validReq.setNewReportNo("RPT-2024-002");
            
            SubcontractValidationUtil.validateReplaceRequest(validReq);
            System.out.println("✓ 正常参数校验通过");
        } catch (Exception e) {
            System.out.println("✗ 正常参数校验失败: " + e.getMessage());
        }
        
        // 2. 测试请求对象为空
        try {
            SubcontractValidationUtil.validateReplaceRequest(null);
            System.out.println("✗ 请求对象为空校验失败");
        } catch (ReportDataCheckException e) {
            System.out.println("✓ 请求对象为空校验通过: " + e.getMessage());
        }
        
        // 3. 测试新旧报告号相同
        try {
            SubcontractReplaceReq req = new SubcontractReplaceReq();
            req.setOrderNo("ORD-2024-001");
            req.setLabCode("GZ");
            req.setReportNo("RPT-2024-001");
            req.setNewReportNo("RPT-2024-001");
            
            SubcontractValidationUtil.validateReplaceRequest(req);
            System.out.println("✗ 新旧报告号相同校验失败");
        } catch (ReportDataCheckException e) {
            System.out.println("✓ 新旧报告号相同校验通过: " + e.getMessage());
        }
        
        // 4. 测试新报告号为空
        try {
            SubcontractReplaceReq req = new SubcontractReplaceReq();
            req.setOrderNo("ORD-2024-001");
            req.setLabCode("GZ");
            req.setReportNo("RPT-2024-001");
            req.setNewReportNo("");
            
            SubcontractValidationUtil.validateReplaceRequest(req);
            System.out.println("✗ 新报告号为空校验失败");
        } catch (ReportDataCheckException e) {
            System.out.println("✓ 新报告号为空校验通过: " + e.getMessage());
        }
    }

    /**
     * 测试无效化请求参数校验
     */
    private static void testInvalidateRequestValidation() {
        System.out.println("\n=== 测试无效化请求参数校验 ===");
        
        // 1. 测试正常情况
        try {
            SubcontractInvalidateReq validReq = new SubcontractInvalidateReq();
            validReq.setOrderNo("ORD-2024-001");
            validReq.setObjectNo("SC-2024-001");
            validReq.setLabCode("GZ");
            validReq.setReportNo("RPT-2024-001");
            
            SubcontractValidationUtil.validateInvalidateRequest(validReq);
            System.out.println("✓ 正常参数校验通过");
        } catch (Exception e) {
            System.out.println("✗ 正常参数校验失败: " + e.getMessage());
        }
        
        // 2. 测试请求对象为空
        try {
            SubcontractValidationUtil.validateInvalidateRequest(null);
            System.out.println("✗ 请求对象为空校验失败");
        } catch (ReportDataCheckException e) {
            System.out.println("✓ 请求对象为空校验通过: " + e.getMessage());
        }
        
        // 3. 测试所有必填字段为空的情况
        try {
            SubcontractInvalidateReq req = new SubcontractInvalidateReq();
            // 所有字段都为空
            
            SubcontractValidationUtil.validateInvalidateRequest(req);
            System.out.println("✗ 必填字段为空校验失败");
        } catch (ReportDataCheckException e) {
            System.out.println("✓ 必填字段为空校验通过: " + e.getMessage());
        }
    }
}
