package com.sgs.testdatabiz.sdk.convert;

import com.sgs.testdatabiz.sdk.convert.impl.DataConvertV1Handler;
import com.sgs.testdatabiz.sdk.input.dto.RdConvertRequestDTO;
import com.sgs.testdatabiz.sdk.output.dto.RdReportDataDTO;
import com.sgs.testdatabiz.sdk.util.Func;
import lombok.extern.slf4j.Slf4j;

import java.util.*;


@Slf4j
public class RdConvertServiceServiceImpl implements RdConvertService {

    private final List<DataConvertHandler> dataConvertHandlerList;

    private final Map<String, List<DataConvertHandler>> dataConvertHandlerMap = new HashMap<>();

    @Override
    public Object handler(RdConvertRequestDTO rdConvertRequestDTO) {

        handlerForVersion(rdConvertRequestDTO);

        return rdConvertRequestDTO.getReportDataDTO();
    }

    protected void handlerForVersion(RdConvertRequestDTO rdConvertRequestDTO) {

        List<DataConvertHandler> handlerList = dataConvertHandlerMap.get(rdConvertRequestDTO.getVersion());
        if (Func.isEmpty(handlerList)) {
            return;
        }

        for (DataConvertHandler handler : handlerList) {
            handler.handler(rdConvertRequestDTO);
        }
    }


    private void addConvertHandler(String version, DataConvertHandler dataConvertHandler) {
        List<DataConvertHandler> handlerList = dataConvertHandlerMap.get(version);
        if (Func.isEmpty(handlerList)) {
            handlerList = new ArrayList<>();
            dataConvertHandlerMap.put(version, handlerList);
        }

        handlerList.add(dataConvertHandler);
    }

    public RdConvertServiceServiceImpl() {
        dataConvertHandlerList  =  new ArrayList<>();
        dataConvertHandlerList.add(new DataConvertV1Handler());
        afterPropertiesSet();
    }
    public void afterPropertiesSet()   {
        dataConvertHandlerList.forEach(dataPreConvertHandler -> dataPreConvertHandler.acceptVersion().forEach(version -> addConvertHandler(version, dataPreConvertHandler)));
    }

}
