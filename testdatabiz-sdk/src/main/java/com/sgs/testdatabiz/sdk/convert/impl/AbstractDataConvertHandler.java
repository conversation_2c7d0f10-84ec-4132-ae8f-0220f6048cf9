package com.sgs.testdatabiz.sdk.convert.impl;

import java.util.Date;

import cn.hutool.core.bean.BeanUtil;
import com.sgs.framework.model.enums.ActiveType;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.testdatabiz.facade.model.dto.rd.invoice.RdInvoiceDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.quotation.RdQuotationRelationshipDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.*;
import com.sgs.testdatabiz.facade.model.enums.ContactUsage;
import com.sgs.testdatabiz.facade.model.enums.ProductDffObjectTypeEnum;
import com.sgs.testdatabiz.sdk.enums.CustomerUsage;
import com.sgs.testdatabiz.sdk.input.dto.RdAttachmentInput;
import com.alibaba.fastjson.JSONArray;

import java.util.*;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSONObject;
import com.sgs.testdatabiz.sdk.convert.DataConvertHandler;
import com.sgs.testdatabiz.sdk.enums.CitationType;
import com.sgs.testdatabiz.sdk.enums.DataTypeEnum;
import com.sgs.testdatabiz.sdk.enums.ReportStatusEnum;
import com.sgs.testdatabiz.sdk.input.dto.*;
import com.sgs.testdatabiz.sdk.output.dto.*;
import com.sgs.testdatabiz.sdk.output.dto.RdAttachmentDTO;
import com.sgs.testdatabiz.sdk.output.dto.RdCustomerDTO;
import com.sgs.testdatabiz.sdk.output.dto.RdOrderDTO;
import com.sgs.testdatabiz.sdk.output.dto.RdReportConclusionDTO;
import com.sgs.testdatabiz.sdk.output.dto.RdReportDTO;
import com.sgs.testdatabiz.sdk.output.dto.RdReportMatrixDTO;
import com.sgs.testdatabiz.sdk.output.dto.RdTestSampleDTO;
import com.sgs.testdatabiz.sdk.output.dto.RdTestSampleGroupDTO;
import com.sgs.testdatabiz.sdk.util.DFFUtil;
import com.sgs.testdatabiz.sdk.util.Func;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class AbstractDataConvertHandler implements DataConvertHandler {

    @Override
    public void handler(RdConvertRequestDTO rdConvertRequestDTO) {
        Object requestJson = rdConvertRequestDTO.getRequestJson();
        RdReportDataDTO reportDataDTO = rdConvertRequestDTO.getReportDataDTO();
        String env = rdConvertRequestDTO.getEnv();
        ReportDataInput dataInput = JSONObject.parseObject(requestJson.toString(), ReportDataInput.class);
        this.convertTrfRel(dataInput, reportDataDTO);
        this.convertQuotation(dataInput, reportDataDTO);
        this.convertReport(dataInput, reportDataDTO);
        this.convertReportExt(dataInput, reportDataDTO);
        this.convertReportProductDff(dataInput, reportDataDTO, env);
        this.convertReportMatrixList(dataInput, reportDataDTO);
        this.convertReportTestResultList(dataInput, reportDataDTO);
        this.convertReportInvoiceList(dataInput, reportDataDTO);
        this.convertOrder(dataInput, reportDataDTO);
        this.convertReportConclusionList(dataInput, reportDataDTO);
        this.convertTestSampleList(dataInput, reportDataDTO);
        this.convertAttachmentList(dataInput, reportDataDTO);
    }

    protected void convertAttachmentList(ReportDataInput dataInput, RdReportDataDTO reportDataDTO) {
        if (Func.isEmpty(dataInput.getHeader().getReportFileList())) {
            return;
        }
        List<RdAttachmentInput> reportFileList = dataInput.getHeader().getReportFileList();
        List<com.sgs.testdatabiz.facade.model.dto.rd.report.RdAttachmentDTO> attachmentList = dataInput.getOrder().getAttachmentList();
        List<RdAttachmentDTO> rdAttachmentDTOS = JSONObject.parseArray(JSONObject.toJSONString(reportFileList), RdAttachmentDTO.class);
        
        if (Func.isNotEmpty(attachmentList)) {
            // 处理order中的附件并设置默认值
            List<RdAttachmentDTO> attachmentDTOS = JSONObject.parseArray(JSONObject.toJSONString(attachmentList), RdAttachmentDTO.class);
            if (Func.isNotEmpty(attachmentDTOS)) {
                attachmentDTOS.forEach(l -> {
                    l.setLabId(Func.isNotEmpty(dataInput.getHeader().getLab()) ? dataInput.getHeader().getLab().getLabId() : null);
                    l.setOrderNo(Func.isNotEmpty(dataInput.getOrder()) ? dataInput.getOrder().getOrderNo() : null);
                });

                rdAttachmentDTOS.addAll(attachmentDTOS);
            }
        }
        reportDataDTO.setAttachmentList(rdAttachmentDTOS);
    }

    protected void convertTestSampleList(ReportDataInput dataInput, RdReportDataDTO reportDataDTO) {
        List<RdTestSampleInput> testSampleList = dataInput.getTestSampleList();
        if (Func.isEmpty(testSampleList)) {
            return;
        }
        List<RdTestSampleDTO> sampleList = new ArrayList<>();
        List<RdTestSampleGroupDTO> sampleGroupDTOS = new ArrayList<>();
        reportDataDTO.setTestSampleGroupList(sampleGroupDTOS);
        reportDataDTO.setTestSampleList(sampleList);

        testSampleList.forEach(
                l -> {
                    RdTestSampleDTO sampleDTO = new RdTestSampleDTO();
                    sampleDTO.setOrderNo(Func.isNotEmpty(dataInput.getOrder()) ? dataInput.getOrder().getOrderNo() : null);
                    sampleDTO.setTestSampleInstanceId(l.getTestSampleInstanceId());
                    sampleDTO.setParentTestSampleId(l.getParentTestSampleId());
                    sampleDTO.setSampleNo(l.getTestSampleNo());
                    sampleDTO.setSampleName(l.getTestSampleName());
                    sampleDTO.setSampleType(l.getTestSampleType());
                    sampleDTO.setSampleSeq(l.getTestSampleSeq());
                    sampleDTO.setCategory(l.getCategory());

                    RdMaterialAttrInput materialAttr = l.getMaterialAttr();
                    if (Func.isNotEmpty(materialAttr)) {
                        String materialDescription = materialAttr.getMaterialDescription();
                        String materialOtherSampleInfo = materialAttr.getMaterialOtherSampleInfo();
                        String materialEndUse = materialAttr.getMaterialEndUse();
                        Integer applicableFlag = materialAttr.getApplicableFlag();
                        String materialRemark = materialAttr.getMaterialRemark();
                        String materialName = materialAttr.getMaterialName();
                        String materialColor = materialAttr.getMaterialColor();
                        String materialTexture = materialAttr.getMaterialTexture();
                        sampleDTO.setDescription(materialDescription);
                        sampleDTO.setComposition(materialTexture);
                        sampleDTO.setColor(materialColor);
                        sampleDTO.setSampleRemark(materialRemark);
                        sampleDTO.setEndUse(materialEndUse);
                        sampleDTO.setMaterial(materialName);
                        sampleDTO.setOtherSampleInfo(materialOtherSampleInfo);
                        sampleDTO.setApplicableFlag(applicableFlag);
                    }

//                    sampleDTO.setSampleTypeLabel();
//                    sampleDTO.setCategoryLabel();
                    sampleDTO.setActiveIndicator(l.getActiveIndicator());
                    sampleDTO.setLastModifiedTimestamp(l.getLastModifiedTimestamp());
                    // 映射 tb_test_sample 表新增字段
                    sampleDTO.setCreatedBy(l.getCreatedBy());
                    sampleDTO.setCreatedDate(l.getCreatedDate());
                    sampleList.add(sampleDTO);

                    List<RdTestSampleGroupInput> testSampleGroupList = l.getTestSampleGroupList();
                    if (Func.isNotEmpty(testSampleGroupList)) {
                        testSampleGroupList.forEach(
                                v -> {
                                    RdTestSampleGroupDTO rdTestSampleGroupDTO = new RdTestSampleGroupDTO();
                                    BeanUtil.copyProperties(v, rdTestSampleGroupDTO);
                                    sampleGroupDTOS.add(rdTestSampleGroupDTO);

                                }
                        );
                    }

                }
        );
    }

    protected void convertReportConclusionList(ReportDataInput dataInput, RdReportDataDTO reportDataDTO) {
        List<RdReportConclusionInput> reportConclusionList = dataInput.getReportConclusionList();
        if (Func.isEmpty(reportConclusionList)) {
            return;
        }
        List<RdReportConclusionDTO> list = new ArrayList<>();
        reportDataDTO.setReportConclusionList(list);
        reportConclusionList.forEach(
                l -> {
                    RdReportConclusionDTO reportConclusionDTO = new RdReportConclusionDTO();
                    BeanUtil.copyProperties(l, reportConclusionDTO);
                    if (Func.isNotEmpty(l.getConclusionLevelId())) {
                        reportConclusionDTO.setConclusionLevel(l.getConclusionLevelId());
                    }
                    list.add(reportConclusionDTO);
                }
        );
    }

    protected void convertOrder(ReportDataInput dataInput, RdReportDataDTO reportDataDTO) {
        com.sgs.testdatabiz.facade.model.dto.rd.report.RdOrderDTO order = dataInput.getOrder();
        if (Func.isEmpty(order)) {
            return;
        }

        convertCustomerList(dataInput, reportDataDTO, order);

        Integer systemId = order.getSystemId();
        String orderId = order.getOrderId();
        String orderNo = order.getOrderNo();
        String rootOrderNo = order.getRootOrderNo();
        String originalOrderNo = order.getOriginalOrderNo();
        Integer orderStatus = order.getOrderStatus();
        Integer serviceType = order.getServiceType();
        String orderType = order.getOrderType();
        Integer operationType = order.getOperationType();
        Integer operationMode = order.getOperationMode();
        String productCategory = order.getProductCategory();
        String productSubCategory = order.getProductSubCategory();
        Integer groupId = order.getGroupId();
        String idbLab = order.getIdbLab();
        Integer tat = order.getTat();
        Date serviceStartDate = order.getServiceStartDate();
        Date testingStartDate = order.getTestingStartDate();
        Date testingEndDate = order.getTestingEndDate();
        Date serviceConfirmDate = order.getServiceConfirmDate();
        Date cuttingExpectDueDate = order.getCuttingExpectDueDate();
        Date orderExpectDueDate = order.getOrderExpectDueDate();
        Date jobExpectDueDate = order.getJobExpectDueDate();
        Date subcontractExpectDueDate = order.getSubcontractExpectDueDate();
        Date reportExpectDueDate = order.getReportExpectDueDate();
        Date softCopyDeliveryDate = order.getSoftCopyDeliveryDate();
        Date createDate = order.getCreateDate();
        RdPaymentDTO payment = Optional.ofNullable(order.getPayment()).orElse(new RdPaymentDTO());

        List<RdContactPersonDTO> contactPersonList = order.getContactPersonList();
        RdOrderOthersDTO others = order.getOthers();
        Integer indicator = order.getActiveIndicator();
        RdOrderDTO orderDTO = new RdOrderDTO();
        orderDTO.setLabCode(Func.isEmpty(orderDTO.getLabCode()) ? dataInput.getHeader().getLab().getLabCode() : orderDTO.getLabCode());
        orderDTO.setLabId(Func.isEmpty(orderDTO.getLabId()) ? dataInput.getHeader().getLab().getLabId() : orderDTO.getLabId());
        orderDTO.setBuCode(Func.isEmpty(orderDTO.getBuCode()) ? dataInput.getHeader().getLab().getBuCode() : orderDTO.getBuCode());
        if (Func.isNotEmpty(order.getCustomerList())) {
            com.sgs.testdatabiz.facade.model.dto.rd.report.RdCustomerDTO buyer = order.getCustomerList().stream().filter(l -> Objects.equals(l.getCustomerUsage(), CustomerUsage.Buyer.getUsage())).findFirst().orElse(new com.sgs.testdatabiz.facade.model.dto.rd.report.RdCustomerDTO());
            orderDTO.setKaCustomerDeptCode(buyer.getMarketSegmentCode());
        }
        orderDTO.setToDmFlag(Func.isNotEmpty(order.getFlags()) ? order.getFlags().getToDMFlag() : null);
        orderDTO.setLocationCode(Func.isNotEmpty(dataInput.getHeader().getLab()) ? dataInput.getHeader().getLab().getLocationCode() : null);
        orderDTO.setOrderId(orderId);
        orderDTO.setRootOrderNo(rootOrderNo);
        orderDTO.setOrderNo(orderNo);
        orderDTO.setCreatedDate(createDate);
        orderDTO.setCreatedBy(order.getCreateBy());

//        orderDTO.setOldOrderNo(order.getOldOrderNo());
//        orderDTO.setBossOrderNo(order.getBossOrderNo());
        com.sgs.testdatabiz.facade.model.dto.rd.report.RdOrderDTO.RdRelationshipDTO relationship = order.getRelationship();
        if (Func.isNotEmpty(relationship) && Func.isNotEmpty(relationship.getParent())) {
            if (Func.isNotEmpty(relationship.getParent().getParcelNoList())) {
                orderDTO.setParcelNo(relationship.getParent().getParcelNoList().stream().collect(Collectors.joining(",")));
            }
        }
        if (Func.isNotEmpty(contactPersonList)) {
            RdContactPersonDTO rdContactPersonInput = contactPersonList.stream().filter(l -> Objects.equals(l.getContactUsage(), ContactUsage.CS.getType().toString())).findFirst().orElse(new RdContactPersonDTO());
            orderDTO.setCsName(rdContactPersonInput.getContactName());
            orderDTO.setResponsibleTeamCode(rdContactPersonInput.getResponsibleTeamCode());
        }
        orderDTO.setOrderType(orderType);
        orderDTO.setSystemId(systemId);
        orderDTO.setIdbLab(idbLab);
        orderDTO.setQuoteCurrencyId(payment.getCurrency());
        orderDTO.setTopsLabId(order.getTopsLabId());
        orderDTO.setExecLabCode(order.getTopsLabCode());
//        List<RdProcessListDTO> processList = order.getProcessList();
//        if (Func.isNotEmpty(processList)) {
//            RdProcessDTO confirm = processList.get(0).getConfirm();
//            RdProcessDTO sampleReceive = processList.get(0).getSampleReceive();
//            if (Func.isNotEmpty(confirm)) {
//                orderDTO.setSampleConfirmDate(confirm.getOperationTime());
//            }
//            if (Func.isNotEmpty(sampleReceive)) {
//                orderDTO.setSampleReceivedDate(sampleReceive.getOperationTime());
//            }
//        }
        orderDTO.setSampleConfirmDate(order.getSampleReceiveDate());
        orderDTO.setOrderStatus(orderStatus);
        orderDTO.setServiceLevel(serviceType);
        orderDTO.setSelfTestFlag(Func.isNotEmpty(order.getFlags()) ? order.getFlags().getSelfTestFlag() : null);
        orderDTO.setOperationType(operationType);
        orderDTO.setTat(tat);
        orderDTO.setServiceStartDate(serviceStartDate);
        orderDTO.setTotalAmount(Func.isNotEmpty(order.getPayment()) ? order.getPayment().getTotalAmount() : null);
        orderDTO.setRemark(Func.isNotEmpty(order.getOthers()) ? order.getOthers().getOrderRemark() : null);
        RdCustomerDTO applicant = findCustomer(reportDataDTO.getCustomerList(), CustomerUsage.Applicant.getUsage());
        orderDTO.setApplicantName(applicant.getCustomerName());
        orderDTO.setApplicantBossNo(applicant.getBossNo());
        RdCustomerDTO payer = findCustomer(reportDataDTO.getCustomerList(), CustomerUsage.Payer.getUsage());
        orderDTO.setPayerName(payer.getCustomerName());
        orderDTO.setPayerBossNo(payer.getBossNo());
        RdCustomerDTO buyer = findCustomer(reportDataDTO.getCustomerList(), CustomerUsage.Buyer.getUsage());
        orderDTO.setBuyerName(buyer.getCustomerName());
        orderDTO.setBuyerBossNo(buyer.getBossNo());
        orderDTO.setBuyerGroupName(buyer.getCustomerGroupName());
        orderDTO.setBuyerGroupCode(buyer.getCustomerGroupCode());
        RdCustomerDTO agent = findCustomer(reportDataDTO.getCustomerList(), CustomerUsage.Agent.getUsage());
        orderDTO.setAgentName(agent.getCustomerName());
        orderDTO.setAgentBossNumber(agent.getBossNo());
        orderDTO.setAgentGroupName(agent.getCustomerGroupName());
        orderDTO.setAgentGroupCode(agent.getCustomerGroupCode());
        orderDTO.setActualTat(order.getActualTat());
        orderDTO.setCaseDueDate(order.getOrderExpectDueDate());
        if (Func.isNotEmpty(others)) {
            RdDelayDTO delay = others.getDelay();
            RdCancelDTO cancel = others.getCancel();
            RdPendingDTO pending = others.getPending();
            if (Func.isNotEmpty(delay)) {
                orderDTO.setDelayDay(delay.getDelayDays());
                orderDTO.setDelayReason(delay.getDelayRemark());
            }
            if (Func.isNotEmpty(cancel)) {
                orderDTO.setReasonsForCancelOrder(cancel.getCancelRemark());
            }
            if (Func.isNotEmpty(pending)) {
                orderDTO.setReasonsForOnHold(pending.getPendingRemark());
            }
        }
        orderDTO.setActiveIndicator(indicator);
        orderDTO.setOrderInstanceId(orderId);
        orderDTO.setProductSubCategory(order.getProductSubCategory());
        orderDTO.setProductCategory(order.getProductCategory());
        reportDataDTO.setOrder(orderDTO);
    }

    private static RdCustomerDTO findCustomer(List<RdCustomerDTO> customerDTOS, Integer usage) {
        if (Func.isEmpty(customerDTOS)) {
            return new RdCustomerDTO();
        }
        return customerDTOS.stream().filter(l -> Objects.equals(l.getCustomerUsage(), usage)).findFirst().orElse(new RdCustomerDTO());
    }

    private static void convertCustomerList(ReportDataInput dataInput, RdReportDataDTO reportDataDTO, com.sgs.testdatabiz.facade.model.dto.rd.report.RdOrderDTO order) {
        List<com.sgs.testdatabiz.facade.model.dto.rd.report.RdCustomerDTO> customerList = order.getCustomerList();
        if (Func.isEmpty(customerList)) {
            return;
        }
        List<RdCustomerDTO> rdCustomerDTOS = new ArrayList<>();
        reportDataDTO.setCustomerList(rdCustomerDTOS);
        for (com.sgs.testdatabiz.facade.model.dto.rd.report.RdCustomerDTO customer : customerList) {

            Integer customerUsage = customer.getCustomerUsage();
            Long bossNo = customer.getBossNo();
            String customerGroupCode = customer.getCustomerGroupCode();
            String customerName = customer.getCustomerName();
            String customerAddress = customer.getCustomerAddress();
            List<RdCustomerLanguageDTO> languageList = customer.getLanguageList();
            List<RdCustomerContactDTO> customerContactList = customer.getCustomerContactList();
            String marketSegmentCode = customer.getMarketSegmentCode();
            String customerGroupName = customer.getCustomerGroupName();
            String marketSegmentName = customer.getMarketSegmentName();
            String customerRefId = customer.getCustomerRefId();
            String customerInstanceId = customer.getCustomerInstanceId();
            Date lastModifiedTimestamp = customer.getLastModifiedTimestamp();
            Integer activeIndicator = customer.getActiveIndicator();
            if (Func.isNotEmpty(customerContactList)) {
                customerContactList.forEach(
                        customerContact -> {
                            String customerContactId = customerContact.getCustomerContactId();
                            String customerContactAddressId = customerContact.getCustomerContactAddressId();
                            Long bossContactId = customerContact.getBossContactId();
                            Long bossSiteUseId = customerContact.getBossSiteUseId();
                            String contactName = customerContact.getContactName();
                            String contactTelephone = customerContact.getContactTelephone();
                            String contactMobile = customerContact.getContactMobile();
                            String contactFAX = customerContact.getContactFAX();
                            String contactEmail = customerContact.getContactEmail();

                            RdCustomerDTO customerDTO = new RdCustomerDTO();
                            customerDTO.setCustomerInstanceId(customerInstanceId);
                            customerDTO.setCustomerGroupCode(customerGroupCode);
                            customerDTO.setCustomerGroupName(customerGroupName);
                            customerDTO.setContactPersonName(contactName);
                            customerDTO.setContactPersonEmail(contactEmail);
                            customerDTO.setContactPersonPhone(contactTelephone);
                            customerDTO.setCustomerAddress(customerAddress);
                            customerDTO.setCustomerName(customerName);
                            customerDTO.setBossNo(bossNo);
                            customerDTO.setCustomerUsage(customerUsage);
                            customerDTO.setActiveIndicator(activeIndicator);
                            customerDTO.setMarketSegmentCode(marketSegmentCode);
                            customerDTO.setMarketSegmentName(marketSegmentName);
                            customerDTO.setCustomerRefId(customerRefId);
                            customerDTO.setCustomerLangList(Func.isNotEmpty(languageList) ? JSONArray.parseArray(JSONObject.toJSONString(languageList), RdCustomerLangDTO.class) : null);
                            customerDTO.setLastModifiedTimestamp(lastModifiedTimestamp);
                            customerDTO.setCreatedDate(customer.getCreatedDate());

                            customerDTO.setCustomerUsage(customer.getCustomerUsage());
                            rdCustomerDTOS.add(customerDTO);
                        }
                );
            } else {
                rdCustomerDTOS.addAll(JSONObject.parseArray(JSONObject.toJSONString(customerList), RdCustomerDTO.class));
            }
            if (Func.isNotEmpty(rdCustomerDTOS)) {
                rdCustomerDTOS.forEach(
                        l -> l.setOrderNo(Func.isNotEmpty(dataInput.getOrder()) ? dataInput.getOrder().getOrderNo() : null)
                );
            }

        }
    }

    protected void convertReportInvoiceList(ReportDataInput dataInput, RdReportDataDTO reportDataDTO) {
        List<RdInvoiceDTO> invoiceList = dataInput.getInvoiceList();
        if (Func.isEmpty(invoiceList))
            return;

        List<RdReportInvoiceDTO> reportInvoiceList = new ArrayList<>();
        reportDataDTO.setReportInvoiceList(reportInvoiceList);

        List<RdQuotationDTO> quotationList = reportDataDTO.getQuotationList();
        Map<String, RdQuotationDTO> quotationDTOMap = new HashMap<>();
        if (Func.isNotEmpty(quotationList)) {
            for (RdQuotationDTO quotationDTO : quotationList) {
                quotationDTOMap.put(quotationDTO.getQuotationNo(), quotationDTO);
            }
        }

        List<RdQuotationInvoiceRelDTO> quotationInvoiceRelDTOS = new ArrayList<>();
        reportDataDTO.setQuotationInvoiceRelList(quotationInvoiceRelDTOS);

        invoiceList.forEach(
                l -> {
                    RdReportInvoiceDTO reportInvoiceDTO = new RdReportInvoiceDTO();
                    reportInvoiceDTO.setLabId(Func.isNotEmpty(dataInput.getHeader().getLab()) ? dataInput.getHeader().getLab().getLabId() : null);
                    reportInvoiceDTO.setSystemId(dataInput.getHeader().getSystemId());
                    reportInvoiceDTO.setOrderNo(dataInput.getHeader().getOrderNo());
                    reportInvoiceDTO.setRootOrderNo(dataInput.getHeader().getRootOrderNo());
                    reportInvoiceDTO.setReportNo(dataInput.getHeader().getReportNo());
//                    List<RdProcessListDTO> processList = l.getProcessList();
//                    if (Func.isNotEmpty(processList)) {
//                        RdProcessDTO create = processList.get(0).getCreate();
//                        if (Func.isNotEmpty(create)) {
//                            reportInvoiceDTO.setInvoiceDate(create.getOperationTime());
//                        }
//                    }

                    reportInvoiceDTO.setInvoiceDate(l.getInvoiceDate());
                    reportInvoiceDTO.setBossOrderNo(l.getBossOrderNo());
                    reportInvoiceDTO.setProductCode(l.getProductCode());
                    reportInvoiceDTO.setProjectTemplate(l.getProjectTemplate());
                    reportInvoiceDTO.setCostCenter(l.getCostCenter());
                    reportInvoiceDTO.setInvoiceNo(l.getInvoiceNo());
                    reportInvoiceDTO.setInvoiceInstanceId(l.getInvoiceInstanceId());
                    reportInvoiceDTO.setCurrencyCode(l.getCurrency());
                    reportInvoiceDTO.setNetAmount(l.getNetAmount());
                    reportInvoiceDTO.setVatAmount(l.getVatAmount());
                    reportInvoiceDTO.setTotalAmount(l.getTotalAmount());
                    reportInvoiceDTO.setPrePaidAmount(l.getPrePaidAmount());
                    reportInvoiceDTO.setInvoiceStatus(l.getInvoiceStatus());
//                    reportInvoiceDTO.setProductCodeLabel();
//                    reportInvoiceDTO.setInvoiceStatusLabel();
                    // 映射 tb_report_invoice 表新增字段
                    reportInvoiceDTO.setCreatedDate(l.getInvoiceDate());
                    reportInvoiceList.add(reportInvoiceDTO);

                    List<String> quotationNos = l.getQuotationNos();
                    if (Func.isNotEmpty(quotationNos)) {
                        quotationNos.forEach(
                                quotationNo -> {
                                    RdQuotationDTO quotationDTO = quotationDTOMap.get(quotationNo);
                                    if (Func.isNotEmpty(quotationDTO)) {
                                        RdQuotationInvoiceRelDTO relDTO = new RdQuotationInvoiceRelDTO();
                                        relDTO.setSystemId(dataInput.getHeader().getSystemId());
                                        relDTO.setQuotationNo(quotationNo);
                                        relDTO.setInvoiceNo(l.getInvoiceNo());
                                        relDTO.setQuotationInstanceId(quotationDTO.getQuotationInstanceId());
                                        relDTO.setInvoiceInstanceId(l.getInvoiceInstanceId());
                                        relDTO.setActiveIndicator(ActiveType.Enable.getStatus());
                                        quotationInvoiceRelDTOS.add(relDTO);
                                    }
                                }
                        );
                    }
                }
        );
    }

    protected void convertReportTestResultList(ReportDataInput dataInput, RdReportDataDTO reportDataDTO) {

        RdReportInput header = dataInput.getHeader();

        Map<String, List<RdSpecimenInput>> map = new HashMap<>();
        Map<String, RdPositionInput> positionMap = new HashMap<>();
        Map<String, RdConditionInput> conditionMap = new HashMap<>();

        if (Func.isNotEmpty(header) && Func.isNotEmpty(header.getReportMatrixList())) {
            header.getReportMatrixList().forEach(
                    matrix -> {
                        List<RdSpecimenInput> specimenList = matrix.getSpecimenList();
                        String testMatrixId = matrix.getTestMatrixId();
                        if (Func.isNotEmpty(specimenList)) {
                            map.put(testMatrixId, specimenList);
                        }

                        List<RdPositionInput> positionList = matrix.getPositionList();
                        if (Func.isNotEmpty(positionList)) {
                            positionList.forEach(
                                    position -> {
                                        String positionInstanceId = position.getPositionInstanceId();
                                        positionMap.put(positionInstanceId, position);
                                    }
                            );
                        }

                        List<RdConditionInput> conditionList = matrix.getConditionList();

                        if (Func.isNotEmpty(conditionList)) {
                            conditionList.forEach(
                                    condition -> {
                                        conditionMap.put(condition.getConditionInstanceId(), condition);
                                    }
                            );
                        }
                    });
        }

        Map<String, RdAnalyteInput> analyteInputMap = getRdAnalyteInputMap(dataInput);

        setReportTestResultInfo(dataInput, reportDataDTO, map, analyteInputMap, conditionMap, positionMap);
    }

    private static void setReportTestResultInfo(ReportDataInput dataInput, RdReportDataDTO reportDataDTO, Map<String, List<RdSpecimenInput>> map, Map<String, RdAnalyteInput> analyteInputMap, Map<String, RdConditionInput> conditionMap, Map<String, RdPositionInput> positionMap) {
        List<RdTestResultInput> testResultList = dataInput.getTestResultList();
        if (Func.isEmpty(testResultList)) {
            return;
        }
        List<RdReportTestResultDTO> list = new ArrayList<>();
        reportDataDTO.setReportTestResultList(list);
        testResultList.forEach(
                l -> {
                    RdReportTestResultDTO testResultDTO = new RdReportTestResultDTO();
                    testResultDTO.setLabId(dataInput.getLabId());
                    testResultDTO.setOrderNo(dataInput.getHeader().getOrderNo());
                    testResultDTO.setReportNo(dataInput.getHeader().getReportNo());
                    testResultDTO.setTestMatrixId(l.getTestMatrixId());
                    testResultDTO.setTestResultInstanceId(l.getTestResultInstanceId());
                    testResultDTO.setTestResultFullName(Func.isNotEmpty(l.getTestResult()) ? l.getTestResult().getTestResultFullName() : null);
                    Integer analyteConclusionId = Func.isNotEmpty(l.getTestResult())?l.getTestResult().getAnalyteConclusionId():null;
                    testResultDTO.setConclusionCode(analyteConclusionId);
                    List<RdSpecimenInput> rdSpecimenInputs = map.get(l.getTestMatrixId());
                    if (Func.isNotEmpty(l.getTestResult()) && Func.isNotEmpty(l.getTestResult().getTestResultFullNameRel())) {
                        RdTestResultNameInput testResultFullNameRel = l.getTestResult().getTestResultFullNameRel();
                        if (Func.isNotEmpty(rdSpecimenInputs)) {
                            rdSpecimenInputs.forEach(
                                    specimen -> {
                                        if(Func.isNotEmpty(testResultFullNameRel)){
                                           String specimenInstanceId = testResultFullNameRel.getSpecimenInstanceId();
                                           String upSpecimenInstanceId = testResultFullNameRel.getUpSpecimenInstanceId();
                                           if (Func.isNotEmpty(specimenInstanceId) &&
                                                specimen.getSpecimenInstanceId().equals(specimenInstanceId)) {
                                            testResultDTO.setSpecimen(specimen.getSpecimenDescription());
                                            }
                                            if (Func.isNotEmpty(upSpecimenInstanceId) &&
                                                    specimen.getSpecimenInstanceId().equals(upSpecimenInstanceId)) {
                                                testResultDTO.setUpSpecimen(specimen.getSpecimenDescription());
                                            }
                                        }
                                    });

                        }
                        if(Func.isNotEmpty(testResultFullNameRel)) {
                            if (Func.isNotEmpty(testResultFullNameRel) && Func.isNotEmpty(testResultFullNameRel.getAnalyteInstanceId())) {
                                RdAnalyteInput rdAnalyteInput = analyteInputMap.get(testResultFullNameRel.getAnalyteInstanceId());
                                if (Func.isNotEmpty(rdAnalyteInput)) {
                                    testResultDTO.setAnalyteName(rdAnalyteInput.getAnalyteName());
                                    testResultDTO.setCasNo(rdAnalyteInput.getCasNo());
                                    testResultDTO.setAnalyteId(rdAnalyteInput.getAnalyteId());
                                }
                            }

                            if (Func.isNotEmpty(testResultFullNameRel.getParentConditionInstanceId()) && Func.isNotEmpty(conditionMap.get(testResultFullNameRel.getParentConditionInstanceId()))) {
                                RdConditionInput rdConditionInput = conditionMap.get(testResultFullNameRel.getParentConditionInstanceId());
                                testResultDTO.setConditionParent(rdConditionInput.getConditionName());
                            }
                            if (Func.isNotEmpty(testResultFullNameRel.getConditionInstanceId()) && Func.isNotEmpty(conditionMap.get(testResultFullNameRel.getConditionInstanceId()))) {
                                RdConditionInput rdConditionInput = conditionMap.get(testResultFullNameRel.getConditionInstanceId());
                                testResultDTO.setTestCondition(rdConditionInput.getConditionName());
                            }
                            if (Func.isNotEmpty(testResultFullNameRel.getProcedureConditionInstanceId()) && Func.isNotEmpty(conditionMap.get(testResultFullNameRel.getProcedureConditionInstanceId()))) {
                                RdConditionInput rdConditionInput = conditionMap.get(testResultFullNameRel.getProcedureConditionInstanceId());
                                testResultDTO.setProcedureTestCondition(rdConditionInput.getConditionName());
                            }
                            if (Func.isNotEmpty(testResultFullNameRel.getPositionInstanceId()) && Func.isNotEmpty(positionMap.get(testResultFullNameRel.getPositionInstanceId()))) {
                                RdPositionInput rdPositionInput = positionMap.get(testResultFullNameRel.getPositionInstanceId());
                                testResultDTO.setTestPosition(rdPositionInput.getPositionName());
                            }
                        }
                    }
                    testResultDTO.setResultValue(Func.isNotEmpty(l.getTestResult()) ? l.getTestResult().getResultValue() : null);
                    testResultDTO.setResultValueRemark(Func.isNotEmpty(l.getTestResult()) ? l.getTestResult().getResultValueRemark() : null);
                    testResultDTO.setResultUnit(Func.isNotEmpty(l.getTestResult()) ? l.getTestResult().getResultUnit() : null);
                    testResultDTO.setFailFlag(Func.isNotEmpty(l.getTestResult()) ? l.getTestResult().getFailFlag() : null);
                    testResultDTO.setLimitValueFullName(Func.isNotEmpty(l.getReportLimit()) ? l.getReportLimit().getLimitValueFullName() : null);
                    testResultDTO.setLimitUnit(Func.isNotEmpty(l.getReportLimit()) ? l.getReportLimit().getLimitUnit() : null);
                    testResultDTO.setTestResultSeq(l.getTestResultSeq());
                    testResultDTO.setFailRemark(Func.isNotEmpty(l.getTestResult()) ? l.getTestResult().getFailRemark() : null);
                    testResultDTO.setReportRemark(Func.isNotEmpty(l.getTestResult()) ? l.getTestResult().getReportRemark() : null);
//                    testResultDTO.setBizVersionId();
                    //SCI-1378
                    testResultDTO.setCasNo(Func.isNotEmpty(l.getCasNo()) ? l.getCasNo() : null);
                    // 映射 tb_report_test_result 表新增字段
                    testResultDTO.setCreatedBy(l.getCreatedBy());
                    testResultDTO.setCreatedDate(l.getCreatedDate());
                    testResultDTO.setTestData(l.getTestData());
                    setMethodLimitAndReferInfo(l, testResultDTO);
                    setTestResultLanguageList(l, testResultDTO);
                    list.add(testResultDTO);
                }
        );
    }

    private static void setMethodLimitAndReferInfo(RdTestResultInput l, RdReportTestResultDTO testResultDTO) {
        if(l.getMethodLimit()!=null){
            testResultDTO.setMdl(l.getMethodLimit().getLimitValueFullName());
        }
        if(Func.isNotEmpty(l.getShareDataRefer())) {
            StringJoiner referFromReportNos = new StringJoiner(",");
            List<String> referFromReportNoList = l.getShareDataRefer().getReferFromReportNo();
            if(Func.isNotEmpty(referFromReportNoList)&& !referFromReportNoList.isEmpty()){
                referFromReportNoList.forEach(
                        referFromReportNos::add
                );
            }

            String referFromReportNo = referFromReportNos.toString();
            testResultDTO.setReferFormReportNo(referFromReportNo);
            testResultDTO.setReferFormOrderNo(l.getShareDataRefer().getReferFromOrderNo());
            testResultDTO.setReferFormSampleNo(l.getShareDataRefer().getReferFromSampleNo());
        }
        testResultDTO.setMetaData(l.getMetaData());
    }

    private static Map<String, RdAnalyteInput> getRdAnalyteInputMap(ReportDataInput dataInput) {
        Map<String, RdAnalyteInput> analyteInputMap = new HashMap<>();
        List<RdTestLineInput> testLineList = dataInput.getTestLineList();
        if (Func.isNotEmpty(testLineList)) {
            testLineList.forEach(
                    testLine -> {
                        List<RdAnalyteInput> analyteList = testLine.getAnalyteList();
                        if (Func.isNotEmpty(analyteList)) {
                            analyteList.forEach(
                                    analyte -> analyteInputMap.put(analyte.getAnalyteInstanceId(), analyte)
                            );
                        }

                    }
            );
        }
        return analyteInputMap;
    }

    private static void setTestResultLanguageList(RdTestResultInput l, RdReportTestResultDTO testResultDTO) {
        List<RdReportTestResultLangDTO> languageList = new ArrayList<>();
        testResultDTO.setReportTestResultLangList(languageList);
        if(Func.isNotEmpty(l.getLanguageList())){
            l.getLanguageList().forEach(
                    r -> {
                        RdReportTestResultLangDTO languageDTO = new RdReportTestResultLangDTO();
                        languageDTO.setLanguageId(r.getLanguageId());
                        languageDTO.setTestResultFullName(r.getTestResultFullName());
                        languageDTO.setLimitValueFullName(r.getLimitValueFullName());
                        languageDTO.setAnalyteName(r.getTestAnalyteName());
                        languageList.add(languageDTO);
                    }
            );
        }
    }

    protected void convertReportMatrixList(ReportDataInput dataInput, RdReportDataDTO reportDataDTO) {
        List<RdReportMatrixInput> reportMatrixList = dataInput.getHeader().getReportMatrixList();
        if (Func.isEmpty(reportMatrixList)) {
            return;
        }
        List<RdTestLineInput> testLineList = dataInput.getTestLineList();
        Map<String, RdTestLineInput> testLineMap = new HashMap<>();
        if (Func.isNotEmpty(testLineList)) {
            testLineList.forEach(
                    l -> {
                        String testLineInstanceId = l.getTestLineInstanceId();
                        testLineMap.put(testLineInstanceId, l);
                    }
            );
        }

        Map<String, RdTestSampleInput> testSampleMap = getRdTestSampleInputMap(dataInput);

        List<RdReportMatrixDTO> list = new ArrayList<>();
        reportDataDTO.setReportMatrixList(list);
        reportMatrixList.forEach(
                l -> {
                    RdReportMatrixDTO reportMatrixDTO = new RdReportMatrixDTO();
                    reportMatrixDTO.setLabId(dataInput.getLabId());
                    reportMatrixDTO.setOrderNo(dataInput.getHeader().getOrderNo());
                    reportMatrixDTO.setReportNo(dataInput.getHeader().getReportNo());
                    reportMatrixDTO.setTestMatrixId(l.getTestMatrixId());
                    reportMatrixDTO.setTestMatrixGroupId(l.getTestMatrixGroupId());
                    setReportMatrixTestLineInfo(l, testLineMap, reportMatrixDTO);
//                    reportMatrixDTO.setMethodDesc();
//                    reportMatrixDTO.setTestLineMappingId();
                    setReportMatrixConditionInfo(l, reportMatrixDTO);
                    setReportMatrixExternalInfo(l, reportMatrixDTO);


                    reportMatrixDTO.setSampleInstanceId(l.getTestSampleInstanceId());
                    setReportMatrixTestSampleInfo(l, testSampleMap, reportMatrixDTO);
                    setReportMatrixConlusionInfo(l, reportMatrixDTO);

//                    reportMatrixDTO.setPpNo();
//                    reportMatrixDTO.setPpVersionId();
//                    reportMatrixDTO.setAid();
//                    reportMatrixDTO.setMatrixStatus();
//                    reportMatrixDTO.setBizVersionId();
//                    reportMatrixDTO.setTestLineAttribute();
//                    reportMatrixDTO.setSampleTypeLabel();
//                    reportMatrixDTO.setCategoryLabel();
//                    reportMatrixDTO.setConclusionCodeLabel();
//                    reportMatrixDTO.setMatrixStatusLabel();
                    reportMatrixDTO.setActiveIndicator(l.getActiveIndicator());
                    //SCI-1378
                    reportMatrixDTO.setMetaData(l.getMetaData());
                    reportMatrixDTO.setApplicationFactor(l.getApplicationFactor());
                    // 映射 tb_report_matrix 表新增字段
                    reportMatrixDTO.setRdObjectRelId(l.getRdObjectRelId());
                    reportMatrixDTO.setCreatedBy(l.getCreatedBy());
                    reportMatrixDTO.setCreatedDate(l.getCreatedDate());
//                    reportMatrixDTO.setReportMatrixLangList();
                    list.add(reportMatrixDTO);
                }
        );
    }

    private static void setReportMatrixConlusionInfo(RdReportMatrixInput l, RdReportMatrixDTO reportMatrixDTO) {
        RdConclusionInput conclusion = l.getConclusion();
        if (Func.isNotEmpty(conclusion)) {
            reportMatrixDTO.setConclusionCode(conclusion.getConclusionCode());
            reportMatrixDTO.setReviewConclusion(conclusion.getReviewConclusion());
            reportMatrixDTO.setCustomerConclusion(conclusion.getCustomerConclusion());
            reportMatrixDTO.setConclusionRemark(conclusion.getConclusionRemark());
        }
    }

    private static void setReportMatrixTestLineInfo(RdReportMatrixInput l, Map<String, RdTestLineInput> testLineMap, RdReportMatrixDTO reportMatrixDTO) {
        RdTestLineInput rdTestLineInput = testLineMap.get(l.getTestLineInstanceId());
        reportMatrixDTO.setTestLineInstanceId(l.getTestLineInstanceId());
        if (Func.isNotEmpty(rdTestLineInput)) {
            reportMatrixDTO.setTestLineId(rdTestLineInput.getTestLineId());
            reportMatrixDTO.setTestLineType(Func.isNotEmpty(rdTestLineInput.getTestLineType()) ? rdTestLineInput.getTestLineType().longValue() : null);
            reportMatrixDTO.setTestLineStatus(Func.isNotEmpty(rdTestLineInput.getTestLineStatus()) ? rdTestLineInput.getTestLineStatus().toString() : null);
            reportMatrixDTO.setTestLineRemark(rdTestLineInput.getTestLineRemark());
            reportMatrixDTO.setTestLineSeq(Func.isNotEmpty(rdTestLineInput.getTestLineSeq()) ? rdTestLineInput.getTestLineSeq().longValue() : null);
            reportMatrixDTO.setEvaluationAlias(rdTestLineInput.getEvaluationAlias());
            reportMatrixDTO.setEvaluationName(rdTestLineInput.getEvaluationName());
            reportMatrixDTO.setLabSectionName(rdTestLineInput.getLabSectionName());

            setReportMatrixInfoByCitation(rdTestLineInput, reportMatrixDTO);
            setReportMatrixPpTestLine(l, rdTestLineInput, reportMatrixDTO);

        }
    }

    private static Map<String, RdTestSampleInput> getRdTestSampleInputMap(ReportDataInput dataInput) {
        List<RdTestSampleInput> testSampleList = dataInput.getTestSampleList();
        Map<String, RdTestSampleInput> testSampleMap = new HashMap<>();
        if (Func.isNotEmpty(testSampleList)) {
            testSampleList.forEach(
                    testSample -> {
                        String testSampleInstanceId = testSample.getTestSampleInstanceId();
                        testSampleMap.put(testSampleInstanceId, testSample);
                    }
            );
        }
        return testSampleMap;
    }

    private static void setReportMatrixTestSampleInfo(RdReportMatrixInput l, Map<String, RdTestSampleInput> testSampleMap, RdReportMatrixDTO reportMatrixDTO) {
        RdTestSampleInput rdTestSampleInput = testSampleMap.get(l.getTestSampleInstanceId());
        if (Func.isNotEmpty(rdTestSampleInput)) {
//                        List<RdTestSampleGroupInput> testSampleGroupList = rdTestSampleInput.getTestSampleGroupList();
//                        reportMatrixDTO.setSampleGroup();
            reportMatrixDTO.setSampleParentId(rdTestSampleInput.getParentTestSampleId());
            reportMatrixDTO.setSampleNo(rdTestSampleInput.getTestSampleNo());
            reportMatrixDTO.setSampleName(rdTestSampleInput.getTestSampleName());
            reportMatrixDTO.setExternalSampleNo(rdTestSampleInput.getExternalSampleNo());
            reportMatrixDTO.setExternalSampleName(rdTestSampleInput.getExternalSampleName());
            reportMatrixDTO.setSampleType(Func.isNotEmpty(rdTestSampleInput.getTestSampleType()) ? rdTestSampleInput.getTestSampleType().toString() : null);
            reportMatrixDTO.setSampleSeq(Func.isNotEmpty(rdTestSampleInput.getTestSampleSeq()) ? rdTestSampleInput.getTestSampleSeq().toString() : null);
            reportMatrixDTO.setCategory(rdTestSampleInput.getCategory());
            RdMaterialAttrInput materialAttr = rdTestSampleInput.getMaterialAttr();
            if (Func.isNotEmpty(materialAttr)) {

                reportMatrixDTO.setSampleRemark(materialAttr.getMaterialRemark());
                reportMatrixDTO.setComposition(materialAttr.getMaterialTexture());
                reportMatrixDTO.setColor(materialAttr.getMaterialColor());
                reportMatrixDTO.setDescription(materialAttr.getMaterialDescription());
                reportMatrixDTO.setEndUse(materialAttr.getMaterialEndUse());
                reportMatrixDTO.setApplicable(Objects.toString(materialAttr.getApplicableFlag()));
                reportMatrixDTO.setMaterial(materialAttr.getMaterialName());
                reportMatrixDTO.setOtherSampleInfo(materialAttr.getMaterialOtherSampleInfo());
                reportMatrixDTO.setExtFields(materialAttr.getExtFields());
            }
        }
    }

    private static void setReportMatrixExternalInfo(RdReportMatrixInput l, RdReportMatrixDTO reportMatrixDTO) {
        RdReportMatrixExternalInput external = l.getExternal();
        if (Func.isNotEmpty(external)) {
            reportMatrixDTO.setExternalId(external.getTestMatrixId());
//                        reportMatrixDTO.setExternalCode();
        }
    }

    private static void setReportMatrixConditionInfo(RdReportMatrixInput l, RdReportMatrixDTO reportMatrixDTO) {
        List<RdConditionInput> conditionList = l.getConditionList();
        if (Func.isNotEmpty(conditionList)) {
            reportMatrixDTO.setCondition(JSONObject.toJSONString(conditionList));
        }
    }

    private static void setReportMatrixPpTestLine(RdReportMatrixInput l, RdTestLineInput rdTestLineInput, RdReportMatrixDTO reportMatrixDTO) {
        List<RdPpTestLineRelInput> ppTestLineRelList = rdTestLineInput.getPpTestLineRelList();
        if (Func.isNotEmpty(ppTestLineRelList)) {
            List<RdPpTestLineDTO> rdPpTestLineDTOList = new ArrayList<>();
            ppTestLineRelList.forEach(
                    ppTestLineRel -> {
                        RdPpTestLineDTO rdPpTestLineDTO = new RdPpTestLineDTO();
                        BeanUtil.copyProperties(ppTestLineRel, rdPpTestLineDTO);
                        rdPpTestLineDTO.setTestMatrixId(l.getTestMatrixId());
                        rdPpTestLineDTOList.add(rdPpTestLineDTO);
                    }
            );
            reportMatrixDTO.setRdPpTestLineList(rdPpTestLineDTOList);
        }
    }

    private static void setReportMatrixInfoByCitation(RdTestLineInput rdTestLineInput, RdReportMatrixDTO reportMatrixDTO) {
        RdCitationInput citation = rdTestLineInput.getCitation();
        if (Func.isNotEmpty(citation)) {
            reportMatrixDTO.setCitationId(citation.getCitationId());
            reportMatrixDTO.setCitationType(citation.getCitationType());
            reportMatrixDTO.setCitationName(citation.getCitationName());
            reportMatrixDTO.setCitationFullName(citation.getCitationFullName());
            reportMatrixDTO.setCitationVersionId(Func.isNotEmpty(citation.getCitationVersionId()) ? citation.getCitationVersionId().longValue() : null);
            reportMatrixDTO.setCitationSectionId(citation.getCitationSectionId());
            reportMatrixDTO.setCitationSectionName(citation.getCitationSectionName());
        }
    }

    protected void convertReportProductDff(ReportDataInput dataInput, RdReportDataDTO reportDataDTO, String env) {
        com.sgs.testdatabiz.facade.model.dto.rd.report.RdOrderDTO order = dataInput.getOrder();
        RdReportInput rdReportInput = dataInput.getHeader();
        List<RdProductDTO> productList = null;
        List<RdSampleDTO> sampleList = null;
        String orderNo = null;
        String reportNo = dataInput.getHeader().getReportNo();
        if (Func.isNotEmpty(order)) {
            orderNo = order.getOrderNo();
            productList = order.getProductList();
            sampleList = order.getSampleList();
        }
        //如果reportInput有值，则优先使用reportInput的productList和sampleList
        if (Func.isNotEmpty(rdReportInput) && Func.isNotEmpty(rdReportInput.getProductList()) && Func.isNotEmpty(rdReportInput.getSampleList())) {
            productList = rdReportInput.getProductList();
            sampleList = rdReportInput.getSampleList();
        }
        setReportProductDff(productList, sampleList, reportDataDTO, orderNo, reportNo, env);
    }

    private static void setReportProductDff(List<RdProductDTO> productList, List<RdSampleDTO> sampleList, RdReportDataDTO reportDataDTO, String orderNo, String reportNo, String env) {

        List<RdReportProductDffDTO> reportProductDffList = new ArrayList<>();
        reportDataDTO.setReportProductDffList(reportProductDffList);
        if (Func.isNotEmpty(productList)) {
            productList.forEach(
                    l -> {
                        List<RdProductSampleAttrDTO> productAttrList = l.getProductAttrList();
                        String templateId = l.getTemplateId();
                        String formGroupId = DFFUtil.queryDffFormGeneral(l.getTemplateId(), env);
                        String instanceId = l.getProductInstanceId();
                        convertDFF(productAttrList, orderNo, reportNo, templateId, instanceId, formGroupId, reportProductDffList, ProductDffObjectTypeEnum.PRODUCT.getCode());
                    }
            );
        }

        if (Func.isNotEmpty(sampleList)) {
            sampleList.forEach(
                    l -> {
                        List<RdProductSampleAttrDTO> productAttrList = l.getSampleAttrList();
                        String templateId = l.getTemplateId();
                        String formGroupId = DFFUtil.queryDffFormGeneral(templateId, env);
                        String instanceId = l.getTestSampleInstanceId();
                        convertDFF(productAttrList, orderNo, reportNo, templateId, instanceId, formGroupId, reportProductDffList, ProductDffObjectTypeEnum.SAMPLE.getCode());
                    }
            );
        }
    }

    private static void convertDFF(List<RdProductSampleAttrDTO> productAttrList, String orderNo, String reportNo, String l, String
            instanceId, String formGroupId, List<RdReportProductDffDTO> reportProductDffList,Integer objectType) {
        if (Func.isNotEmpty(productAttrList)) {
            productAttrList.forEach(
                    pa -> {
                        setProductDffInfo(orderNo, reportNo, l, instanceId, formGroupId, reportProductDffList, objectType, pa);
                    }
            );
        }
    }

    private static void setProductDffInfo(String orderNo, String reportNo, String l, String instanceId, String formGroupId, List<RdReportProductDffDTO> reportProductDffList, Integer objectType, RdProductSampleAttrDTO pa) {
        List<RdAttrLanguageDTO> languageList = pa.getLanguageList();
        if (Func.isNotEmpty(languageList)) {
            languageList.forEach(
                    lang -> {
                        RdReportProductDffDTO reportProductDffDTO = getRdReportProductDffDTO(orderNo, reportNo, l, instanceId, formGroupId, objectType, pa, lang);
                        reportProductDffList.add(reportProductDffDTO);
                    }
            );
        }else{
            RdReportProductDffDTO reportProductDffDTO = getRdReportProductDffDTO(orderNo, reportNo, l, instanceId, formGroupId, objectType, pa);
            reportProductDffList.add(reportProductDffDTO);
        }
    }

    private static RdReportProductDffDTO getRdReportProductDffDTO(String orderNo, String reportNo, String l, String instanceId, String formGroupId, Integer objectType, RdProductSampleAttrDTO pa, RdAttrLanguageDTO lang) {
        RdReportProductDffDTO reportProductDffDTO = new RdReportProductDffDTO();
        reportProductDffDTO.setObjectInstanceId(instanceId);
        reportProductDffDTO.setOrderNo(orderNo);
        reportProductDffDTO.setReportNo(reportNo);
        reportProductDffDTO.setObjectType(objectType);
        reportProductDffDTO.setFormId(l);
        reportProductDffDTO.setLanguageId(lang.getLanguageId());
        reportProductDffDTO.setLabelCode(pa.getLabelCode());
        reportProductDffDTO.setLabelName(lang.getLabelName());
        reportProductDffDTO.setFieldCode(pa.getLabelCode());
        reportProductDffDTO.setCustomerLabel(lang.getCustomerLabel());
        reportProductDffDTO.setDataType(pa.getDataType());
        reportProductDffDTO.setValue(lang.getValue());
        reportProductDffDTO.setSeq(pa.getSeq());
        reportProductDffDTO.setDisplayInReport(pa.getDisplayInReport());
        reportProductDffDTO.setFormGroupId(formGroupId);
//                                                            reportProductDffDTO.setObjectTypeLabel();
        reportProductDffDTO.setDataTypeLabel(Func.isNotEmpty(DataTypeEnum.findType(pa.getDataType())) ? DataTypeEnum.findType(pa.getDataType()).getDesc() : null);
        return reportProductDffDTO;
    }

    private static RdReportProductDffDTO getRdReportProductDffDTO(String orderNo, String reportNo, String l, String instanceId, String formGroupId, Integer objectType, RdProductSampleAttrDTO pa) {
        RdReportProductDffDTO reportProductDffDTO = new RdReportProductDffDTO();
        reportProductDffDTO.setObjectInstanceId(instanceId);
        reportProductDffDTO.setOrderNo(orderNo);
        reportProductDffDTO.setReportNo(reportNo);
        reportProductDffDTO.setObjectType(objectType);
        reportProductDffDTO.setFormId(l);
        reportProductDffDTO.setLanguageId(LanguageType.English.getLanguageId());
        reportProductDffDTO.setLabelCode(pa.getLabelCode());
        reportProductDffDTO.setLabelName(pa.getLabelName());
        reportProductDffDTO.setFieldCode(pa.getLabelCode());
        reportProductDffDTO.setCustomerLabel(pa.getCustomerLabel());
        reportProductDffDTO.setDataType(pa.getDataType());
        reportProductDffDTO.setValue(pa.getValue());
        reportProductDffDTO.setSeq(pa.getSeq());
        reportProductDffDTO.setDisplayInReport(pa.getDisplayInReport());
        reportProductDffDTO.setFormGroupId(formGroupId);
//                                                            reportProductDffDTO.setObjectTypeLabel();
        reportProductDffDTO.setDataTypeLabel(Func.isNotEmpty(DataTypeEnum.findType(pa.getDataType())) ? DataTypeEnum.findType(pa.getDataType()).getDesc() : null);
        return reportProductDffDTO;
    }

    protected void convertReportExt(ReportDataInput dataInput, RdReportDataDTO reportDataDTO) {
        RdReportExtDTO rdReportExtDTO = new RdReportExtDTO();
        rdReportExtDTO.setRequestJson(JSONObject.toJSONString(dataInput));
        reportDataDTO.setReportExt(rdReportExtDTO);
    }

    protected void convertReport(ReportDataInput dataInput, RdReportDataDTO reportDataDTO) {
        RdReportInput header = dataInput.getHeader();

        RdReportDTO reportDTO = new RdReportDTO();
        if (Func.isNotEmpty(dataInput.getOrder().getServiceRequirement()) && Func.isNotEmpty(dataInput.getOrder().getServiceRequirement().getReport())) {
            reportDTO.setReportHeader(dataInput.getOrder().getServiceRequirement().getReport().getReportHeader());
            reportDTO.setReportAddress(dataInput.getOrder().getServiceRequirement().getReport().getReportAddress());
        }
        reportDTO.setSoftcopyDeliveryDate(header.getSoftCopyDeliveryDate());
        com.sgs.testdatabiz.facade.model.dto.rd.report.RdOrderDTO order = dataInput.getOrder();
        if (Func.isNotEmpty(order) && Func.isNotEmpty(order.getServiceRequirement()) && Func.isNotEmpty(order.getServiceRequirement().getReport())) {
            RdServiceRequirementReportDTO report = order.getServiceRequirement().getReport();
            RdDeliveryDTO softcopy = report.getSoftcopy();
            if (Func.isNotEmpty(softcopy)) {
                reportDTO.setReportDeliveredTo(softcopy.getDeliveryTo());
            }
        }
        reportDTO.setReportCertificateList(header.getReportCertificateList());
        reportDTO.setActiveIndicator(header.getActiveIndicator());
//        reportDTO.setReportLangList();
        reportDTO.setLastModifiedTimestamp(header.getLastModifiedTimestamp());
        reportDTO.setReportInstanceId(header.getReportId());
        reportDTO.setReportRemark(header.getReportRemark());
        reportDTO.setActualTat(header.getActualTat());
        RdDelayDTO delay = order != null && order.getOthers() != null ? order.getOthers().getDelay() : null;
        if (delay != null) {
            reportDTO.setDelayDay(delay.getDelayDays());
            reportDTO.setDelayReason(delay.getDelayRemark());
        }
        //判断是否header中的lab为空且labId是否存在,如果lab中的LabId为空则返回空
        RdLabInput lab = header.getLab();
        Long labId = null;
        if(Func.isNotEmpty(lab)){
            labId = Func.isNotEmpty(lab.getLabId())?lab.getLabId():null;
        }
        reportDTO.setLabId(labId);
        //reportDTO.setLabId(Func.isNotEmpty(header.getLab()) ? Long.parseLong(header.getLab().getLabId().toString()) : null);
        reportDTO.setReportId(header.getReportId());
        reportDTO.setReportNo(header.getReportNo());
        reportDTO.setOriginalReportNo(header.getOriginalReportNo());
        reportDTO.setReportStatus(header.getReportStatus());
        reportDTO.setReportStatusLabel(Func.isNotEmpty(ReportStatusEnum.getCode(header.getReportStatus())) ? ReportStatusEnum.getCode(header.getReportStatus()).getMessage() : null);
        reportDTO.setReportCertificateName(header.getCertificateName());
        reportDTO.setReportCreatedBy(header.getCreateBy());
        reportDTO.setReportCreatedDate(header.getCreateDate());
        reportDTO.setReportApproveBy(header.getApproveBy());
        reportDTO.setReportApproveDate(header.getApproveDate());
        reportDTO.setSoftcopyDeliveryDate(header.getSoftCopyDeliveryDate());

        //SCI-1378
        reportDTO.setReportType(header.getReportType());
        reportDTO.setReportSource(header.getReportSource());
        reportDTO.setTestingType(header.getTestingType());
        reportDTO.setCountryOfDestination(header.getCountryOfDestination());
        reportDTO.setMetaData(header.getMetaData());
        if(Func.isNotEmpty(header.getCertificate())){
            reportDTO.setReportCertificateName(header.getCertificate().getCertificateName()==null?header.getCertificateName():header.getCertificate().getCertificateName());
            reportDTO.setCertificateAcceditationRemark(header.getCertificate().getAcceditationRemark());
            reportDTO.setCertificateApprovedStatement(header.getCertificate().getReportApprovedStatement());
            reportDTO.setCertificateDescription(header.getCertificate().getDescription());

        }
        reportDTO.setSignList(header.getSignList());

        if (Func.isNotEmpty(header.getConclusion()
        )) {
            reportDTO.setConclusionCode(header.getConclusion().getConclusionCode());
            reportDTO.setCustomerConclusion(header.getConclusion().getCustomerConclusion());
            reportDTO.setReviewConclusion(header.getConclusion().getReviewConclusion());
            reportDTO.setConclusionRemark(header.getConclusion().getConclusionRemark());
        }
        reportDataDTO.setHeader(reportDTO);

    }

    protected void convertTrfRel(ReportDataInput dataInput, RdReportDataDTO reportDataDTO) {
        List<RdTrfInput> trfList = dataInput.getTrfList();
        if (Func.isNotEmpty(trfList)) {
            String reportNo = dataInput.getHeader().getReportNo();
            List<RdReportTrfRelDTO> reportTrfRelList = new ArrayList<>();
            reportDataDTO.setReportTrfRelList(reportTrfRelList);

            trfList.forEach(
                    trf -> {
                        List<RdOrderRelInput> orderList = trf.getOrderList();
                        if (Func.isNotEmpty(orderList)) {
                            orderList.forEach(
                                    order -> {
                                        RdReportTrfRelDTO trfRelDTO = new RdReportTrfRelDTO();
                                        trfRelDTO.setTrfRefSystemId(trf.getRefSystemId());
                                        trfRelDTO.setTrfNo(trf.getTrfNo());
                                        trfRelDTO.setTrfTemplateOwner(trf.getTrfTemplateOwner());
                                        trfRelDTO.setOrderSystemId(order.getSystemId());
                                        trfRelDTO.setOrderNo(order.getOrderNo());
                                        trfRelDTO.setReportNo(reportNo);
                                        trfRelDTO.setTrfTemplateId(Func.isNotEmpty(trf.getTrfTemplateId()) ? Long.parseLong(trf.getTrfTemplateId()) : null);
                                        trfRelDTO.setTrfTemplateName(trf.getTrfTemplateName());
                                        reportTrfRelList.add(trfRelDTO);
                                    }
                            );
                        }


                    }
            );
        }

    }


    protected void convertQuotation(ReportDataInput dataDTO, RdReportDataDTO reportDataDTO) {
        List<RdQuotationInput> quotationList = dataDTO.getQuotationList();
        dataDTO.setQuotationList(quotationList);
        if (Func.isNotEmpty(quotationList)) {
            List<RdQuotationDTO> quotationDTOList = new ArrayList<>();
            reportDataDTO.setQuotationList(quotationDTOList);

            quotationList.forEach(
                    quotation -> {
                        List<RdServiceItemInput> serviceItemList = quotation.getServiceItemList();
                        if (Func.isNotEmpty(serviceItemList)) {
                            serviceItemList.forEach(
                                    serviceItem -> {
                                        RdQuotationDTO quotationDTO = new RdQuotationDTO();
                                        quotationDTO.setQuotationInstanceId(quotation.getQuotationInstanceId());
                                        quotationDTOList.add(quotationDTO);
//                                        quotationDTO.setLabId();
                                        quotationDTO.setSystemId(Long.parseLong(quotation.getSystemId().toString()));
                                        //这里做了转换orderNo标识RealOrderNo，RootOrderNo标识logicOrderNo
                                        quotationDTO.setOrderNo(dataDTO.getOrder().getOrderNo());
                                        quotationDTO.setRootOrderNo(dataDTO.getOrder().getRootOrderNo());
                                        quotationDTO.setReportNo(dataDTO.getHeader().getReportNo());
                                        quotationDTO.setQuotationNo(quotation.getQuotationNo());
                                        quotationDTO.setCurrencyCode(quotation.getCurrency());
                                        if (Func.isNotEmpty(quotation.getPayer())) {
                                            quotationDTO.setPayerCustomerName(quotation.getPayer().getCustomerName());
                                            quotationDTO.setPayerBossNumber(quotation.getPayer().getBossNo());
                                        }
                                        quotationDTO.setServiceItemInstanceId(serviceItem.getServiceItemInstanceId());
                                        quotationDTO.setServiceItemType(serviceItem.getServiceItemType());
                                        quotationDTO.setServiceItemName(serviceItem.getServiceItemName());
                                        quotationDTO.setServiceItemSeq(serviceItem.getServiceItemSeq());
//                                        quotationDTO.setSpecialOfferFlag();
                                        quotationDTO.setQuantity(serviceItem.getQuantity());
                                        quotationDTO.setServiceItemListUnitPrice(serviceItem.getServiceItemListUnitPrice());
                                        quotationDTO.setServiceItemSalesUnitPrice(serviceItem.getServiceItemSalesUnitPrice());
                                        quotationDTO.setServiceItemDiscount(serviceItem.getServiceItemDiscount());
                                        quotationDTO.setServiceItemNetAmount(serviceItem.getServiceItemNetAmount());
                                        quotationDTO.setServiceItemVatAmount(serviceItem.getServiceItemVATAmount());
                                        quotationDTO.setServiceItemTotalAmount(serviceItem.getServiceItemTotalAmount());
//                                        quotationDTO.setSumNetAmount();
//                                        quotationDTO.setSumVatAmount();
//                                        quotationDTO.setTotalAmount();
                                        quotationDTO.setAdjustmentAmount(quotation.getAdjustmentAmount());
//                                        quotationDTO.setAdjustmentDiscount();
                                        quotationDTO.setFinalAmount(quotation.getFinalAmount());
                                        quotationDTO.setPpNo(serviceItem.getPpNo());
                                        quotationDTO.setPpName(serviceItem.getPpName());
                                        quotationDTO.setTestLineId(serviceItem.getTestLineId());
                                        quotationDTO.setCitationType(String.valueOf(serviceItem.getCitationType()));
                                        quotationDTO.setCitationId(serviceItem.getCitationId());
                                        quotationDTO.setCitationName(serviceItem.getCitationName());
//                                        quotationDTO.setCitationFullName();
                                        quotationDTO.setQuotationVersionId(quotation.getQuotationVersionId());
                                        quotationDTO.setQuotationStatus(quotation.getQuotationStatus());
                                        //SCI-1469
                                        quotationDTO.setActiveIndicator(quotation.getActiveIndicator());
                                        quotationDTO.setServiceItemExchangeRatePrice(serviceItem.getExchangeRate());
                                        quotationDTO.setServiceItemSurChargePrice(serviceItem.getSurCharge());
                                        // 映射 tb_quotation 表新增字段
                                        quotationDTO.setCreatedBy(quotation.getCreatedBy());
                                        quotationDTO.setCreatedDate(quotation.getCreatedDate());

//                                        quotationDTO.setQuotationStatusLabel();
                                        List<RdServiceItemLanguageInput> languageList = serviceItem.getLanguageList();
//                                        quotationDTO.setQuotationLangList();
                                        Integer citationType = serviceItem.getCitationType();
                                        CitationType type = CitationType.findType(citationType);
                                        if (Func.isNotEmpty(type)) {
                                            quotationDTO.setCitationTypeLabel(type.getMessage());
                                        }
                                    }
                            );
                            RdQuotationRelationshipDTO relationship = quotation.getRelationship();
                            if (Func.isNotEmpty(relationship) && Func.isNotEmpty(relationship.getParallel()) && Func.isNotEmpty(relationship.getParallel().getBossOrderList())) {
                                List<RdQuotationRelationshipDTO.RdQuotationRelParallelDTO.RdQuotationRelParallelOrderDTO> bossOrderList = relationship.getParallel().getBossOrderList();
                                bossOrderList.forEach(
                                        bossOrder -> {
                                            String bossOrderNo = bossOrder.getBossOrderNo();
                                            String relBossOrderNo = bossOrder.getRefBossOrderNo();

                                            quotationDTOList.forEach(
                                                    quotationDTO -> {
                                                        quotationDTO.setBossOrderNo(bossOrderNo);
                                                        quotationDTO.setRefBossOrderNo(relBossOrderNo);
                                                    }
                                            );
                                        }
                                );
                            }
                        }
                    }
            );
        }
    }
}
